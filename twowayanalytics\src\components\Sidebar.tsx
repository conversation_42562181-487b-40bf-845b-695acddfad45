import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Go<PERSON>ot<PERSON><PERSON>, Go<PERSON>eople } from "react-icons/go";
import { HiOutlineLink } from "react-icons/hi2";
// import { HiOutlineHome, HiOutlineLink } from "react-icons/hi2";
import { IoIosArrowDown, IoIosArrowUp } from "react-icons/io";
import logoutIcon from "../assets/images/logout.png";
import { RxCross2, RxHamburgerMenu } from "react-icons/rx";
// import { SlSettings } from "react-icons/sl";
// import { SlSettings } from "react-icons/sl";
import { SlBell, SlSettings } from "react-icons/sl";
import { TfiPieChart } from "react-icons/tfi";
import { Link, useLocation, useNavigate } from "react-router-dom";
import sidebarLogo from "../assets/images/sidebar-logo.png";
import logo from "../assets/logo.svg";
import { useAuth } from "../context/authContext";
import Dropdown from "./Dropdown";
// import Modal from "./Modal";
import { FaGlobe } from "react-icons/fa";
import { FaReadme } from "react-icons/fa";
import { GrContactInfo } from "react-icons/gr";
interface MenuItem {
  name: string;
  path?: string;
  icon: React.ReactNode;
  dropdown?: MenuItem[];
}
const Sidebar = ({
  showSidebar = false,
  onClose,
}: {
  showSidebar?: boolean;
  onClose?: () => void;
}) => {

  const { t, i18n } = useTranslation();
  const isMobile = () => window.innerWidth < 767;
  const [expandedMenu, setExpandedMenu] = useState<string | null>(null);
  const [activeMenuItem, setActiveMenuItem] = useState<string | null>(null); // <-- Added
  const location = useLocation();
  const navigate = useNavigate();
  // const [showUserMenu, setShowUserMenu] = useState(false);
  // const [showModal, setShowModal] = useState(false);

  const [isCollapsed, setIsCollapsed] = useState(false);

  const isRouteActive = (item: MenuItem): boolean => {
    if (activeMenuItem === item.name) return true;
    if (item.path === location.pathname) return true;
    if (
      item.dropdown &&
      item.dropdown.some((sub) => sub.path === location.pathname)
    ) {
      return true;
    }
    return false;
  };
  const handleDropdownClick = (item: MenuItem) => {
    setActiveMenuItem(null);
    if (isCollapsed && item.dropdown && item.dropdown.length > 0) {
      navigate(item.dropdown[0].path!);
      setActiveMenuItem(item.name);
      return;
    }
    if (expandedMenu === item.name) {
      setExpandedMenu(null);
    } else {
      setExpandedMenu(item.name);
    }
  };

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
    if (!isCollapsed) {
      setExpandedMenu(null);
    }
  };
  const { logout } = useAuth();
  // const [modalLabel, setModalLabel] = useState("profileSettings");


  // const [formDetails, setFormDetails] = useState({
  //   timezone: "",
  //   inviteMembers: "",
  //   roles: "",
  //   industry: "",
  // });


  const handleLogout = () => {
    navigate("/login");
    logout();
    ["authToken", "UserData", "powerbi_token"].forEach(key => localStorage.removeItem(key));

    // if (!localStorage.getItem("authToken")) {
    // window.location.href = "/login";
    // }
  };

  // const menuItems: MenuItem[] = [
  //   {
  //     name: t("home"),
  //     path: "/dashboard",
  //     icon: <HiOutlineHome className="text-lg" />,
  //   },
  //   {
  //     name: t("analytics"),
  //     icon: <TfiPieChart />,
  //     dropdown: [
  //       {
  //         name: t("main_dashboard"),
  //         path: "/analaytics",
  //         icon: <GoDotFill className="text-xs" />,
  //       },
  //     ],
  //   },
  //   {
  //     name: t("linkEntities"),
  //     icon: <HiOutlineLink className="text-lg" />,
  //     dropdown: [
  //       {
  //         name: t("publications"),
  //         path: "/publications",
  //         icon: <GoDotFill className="text-xs" />,
  //       },
  //       {
  //         name: t("entities"),
  //         path: "/entities",
  //         icon: <GoDotFill className="text-xs" />,
  //       },
  //     ],
  //   },
  //   {
  //     name: t("groupPosts"),
  //     path: "/group-posts",
  //     icon: <GoPeople className="text-lg" />,
  //   },
  //   {
  //     name: t("settings"),
  //     icon: <SlSettings />,
  //     dropdown: [
  //       {
  //         name: t("settingsEntities"),
  //         path: "/settingsentities",
  //         icon: <GoDotFill className="text-xs" />,
  //       },
  //       {
  //         name: t("attributes"),
  //         path: "/attributes",
  //         icon: <GoDotFill className="text-xs" />,
  //       },
  //       {
  //         name: t("profile-settings"),
  //         path: "/profile-settings",
  //         icon: <GoDotFill className="text-xs" />,
  //       },
  //       {
  //         name: t("User Management"),
  //         path: "/user-management",
  //         icon: <GoDotFill className="text-xs" />,
  //       },
  //       // {
  //       //   name: t("allAttributes"),
  //       //   path: "/AllAttributes",
  //       //   icon: <GoDotFill className="text-xs" />,
  //       // },
  //     ],
  //   },
  //   {
  //     name: t("socialMedia"),
  //     path: "/socialmedia",
  //     icon: <FaGlobe className="text-lg" />,
  //   },

  //   {
  //     name: t("alerts"),
  //     icon: <SlBell />,
  //     dropdown: [
  //       {
  //         name: t("metrics"),
  //         path: "/alert/scope",
  //         icon: <GoDotFill className="text-xs" />,
  //       },
  //       {
  //         name: t("sentiment"),
  //         path: "/alerts/sentiment",
  //         icon: <GoDotFill className="text-xs" />,
  //       },
  //       {
  //         name: t("mentions"),
  //         path: "/alerts/mentions",
  //         icon: <GoDotFill className="text-xs" />,
  //       },
  //     ],
  //   },

  //   {
  //     name: t("instructions"),
  //     path: "/instructions",
  //     icon: <FaReadme className="text-lg" />,
  //   },
  //   {
  //     name: t("contactUs"),
  //     path: "/contactUs",
  //     icon: <GrContactInfo className="text-lg" />,
  //   },

  // ];

  const userData = JSON.parse(localStorage.getItem("UserData") || "{}");
  const role = userData.role?.toLowerCase() || "unknown";
  const isInviteDisabled = role === "user";

  const menuItems: MenuItem[] = [
    // {
    //   name: t("home"),
    //   path: "/dashboard",
    //   icon: <HiOutlineHome className="text-lg" />,
    // },
    {
      name: t("analytics"),
      icon: <TfiPieChart />,
      dropdown: [
        {
          name: t("main_dashboard"),
          path: "/analaytics",
          icon: <GoDotFill className="text-xs" />,
        },
      ],
    },
    {
      name: t("linkEntities"),
      icon: <HiOutlineLink className="text-lg" />,
      dropdown: [
        {
          name: t("publications"),
          path: "/publications",
          icon: <GoDotFill className="text-xs" />,
        },
        {
          name: t("entities"),
          path: "/entities",
          icon: <GoDotFill className="text-xs" />,
        },
      ],
    },
    {
      name: t("groupPost"),
      path: "/group-posts",
      icon: <GoPeople className="text-lg" />,
    },
    {
      name: t("settings"),
      icon: <SlSettings />,
      dropdown: [
        {
          name: t("settingsEntities"),
          path: "/settingsentities",
          icon: <GoDotFill className="text-xs" />,
        },
        {
          name: t("attributes"),
          path: "/attributes",
          icon: <GoDotFill className="text-xs" />,
        },
        // Hide profile-settings and user-management for 'user' role
        ...(!isInviteDisabled
          ? [
            {
              name: t("profile-settings"),
              path: "/profile-settings",
              icon: <GoDotFill className="text-xs" />,
            },
            {
              name: t("User Management"),
              path: "/user-management",
              icon: <GoDotFill className="text-xs" />,
            },
          ]
          : []),
      ],
    },
    // Hide entire "Social Media" menu for 'user' role
    ...(!isInviteDisabled
      ? [
        {
          name: t("socialMedia"),
          path: "/socialmedia",
          icon: <FaGlobe className="text-lg" />,
        },
      ]
      : []),
    {
      name: t("alerts"),
      icon: <SlBell />,
      dropdown: [
        {
          name: t("metrics"),
          path: "/alert/Metrics_table",
          icon: <GoDotFill className="text-xs" />,
        },

        {
          name: t("sentiment"),
          path: "/alerts/sentiment_table",
          icon: <GoDotFill className="text-xs" />,
        },
        {
          name: t("mentions"),
          path: "/alerts/mention_table",
          icon: <GoDotFill className="text-xs" />,
        },
        
      ],
    },
    {
      name: t("instructions"),
      path: "/instructions",
      icon: <FaReadme className="text-lg" />,
    },
    {
      name: t("contactUs"),
      path: "/contactUs",
      icon: <GrContactInfo className="text-lg" />,
    },
  ];



  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 767) {
        setIsCollapsed(false);
      }
    };
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);
  return (
    <aside
      style={{
        transition: "width 0.3s ease",
        width: isCollapsed ? "80px" : "225px",
      }}
      className={`xs:max-md:absolute xs:max-md:bg-white xs:max-md:h-screen xs:max-md:rounded-none xs:max-md:z-10
        rounded-3xl cursor-pointer bg-white shadow flex flex-col relative ${showSidebar ? "" : "xs:max-md:hidden"
        }`}
    >
      <div
        className={`py-6 ${isCollapsed
          ? "flex-col-reverse flex gap-5"
          : "flex justify-between items-center pl-4"
          }`}
      >
        {isCollapsed ? (
          <img
            src={sidebarLogo}
            alt="Logo"
            className="object-contain flex mx-auto w-10 h-10"
          />
        ) : (
          <>
            <img
              src={logo}
              alt="Logo"
              className="object-contain max-w-[160px]"
            />
            <RxCross2 className="mr-4 text-2xl xs:max-md:flex hidden" onClick={onClose} />
          </>
        )}
        <RxHamburgerMenu
          className="text-xl xs:max-md:hidden flex mx-auto text-gray-700"
          onClick={toggleSidebar}
        />
      </div>

      {!isCollapsed && (
        <div className="flex xs:max-md:hidden bg-[#F7F9FC] justify-between px-2 py-3 mx-3 rounded-2xl">
          <div className="w-[100px]">
            <Dropdown
              value={localStorage.getItem("i18nextLng") || "en"}
              className="max-h-[45px] border-none"
              options={[
                { label: "English", value: "en" },
                { label: "German", value: "de" },
                { label: "Français", value: "fr" },
              ]}
              iconStyles="text-black"
              onChange={(lang: string | number) =>
                i18n.changeLanguage(String(lang))
              }
            />
          </div>
          <div className="flex-1 flex justify-between items-center">
            <div className="w-[1px] h-[90%] bg-gray-200 ml-2"> </div>
            <div className="relative">
              <span
                className="text-md cursor-pointer"
              >
                {JSON.parse(localStorage.getItem("UserData") || "{}")
                  .userName || "Jhon Doe"}
              </span>
            </div>
            <img src={logoutIcon} alt="logout-icon" onClick={handleLogout} className="w-5 h-5" />


          </div>
        </div>
      )}

      <nav
        className={`flex-1 overflow-y-auto ${isCollapsed ? "pr-2" : "pr-4 mt-2 "
          }`}
      >
        {menuItems.map((item) => {
          const isActive = isRouteActive(item);

          return (
            <div key={item.name} className="mb-1">
              {!item.dropdown ? (
                <Link
                  to={item.path!}
                  onClick={() => {
                    setExpandedMenu(null);
                    setActiveMenuItem(item.name);
                    if (isMobile() && onClose) onClose();
                  }}
                  className={`flex items-center gap-3 px-4 py-4 rounded-r-full transition-colors duration-200 ${isActive ? "bg-lightblue" : "hover:bg-blue-50 text-gray-800 text-sm"
                    }`}
                >
                  <span
                    className={`${isActive
                      ? "bg-lightblue text-primary"
                      : "hover:bg-blue-50 text-gray-800"
                      } ${isCollapsed && "flex mx-auto"}`}
                  >
                    {item.icon}
                  </span>
                  {!isCollapsed && <span className="text-md">{item.name}</span>}
                </Link>
              ) : (
                <>
                  <button
                    onClick={() => handleDropdownClick(item)}
                    className={`flex items-center w-full px-4 py-4 rounded-r-full cursor-pointer transition-colors duration-200 ${isActive
                      ? "bg-lightblue"
                      : expandedMenu === item.name
                        ? "bg-[#e9f9ff] text-gray-800"
                        : "hover:bg-blue-50 text-gray-800"
                      } ${isCollapsed ? "justify-center" : "justify-between"} `}
                  >
                    <div className="flex items-center gap-3">
                      <span
                        className={`${isActive ? "text-primary" : "text-gray-700"
                          } ${isCollapsed && "flex mx-auto"}`}
                      >
                        {item.icon}
                      </span>
                      {!isCollapsed && (
                        <span className="text-md ">{item.name}</span>
                      )}
                    </div>
                    {!isCollapsed && (
                      <>
                        {expandedMenu === item.name ? (
                          <IoIosArrowUp
                            className={`text-md ${isActive ? "text-primary" : "text-gray-700"
                              }`}
                          />
                        ) : (
                          <IoIosArrowDown
                            className={`text-md ${isActive ? "text-primary" : "text-gray-700"
                              }`}
                          />
                        )}
                      </>
                    )}
                  </button>
                  {!isCollapsed && (
                    <div
                      className={`pl-2 mt-1 space-y-1 overflow-hidden transition-all duration-300 ease-in-out ${expandedMenu === item.name
                        ? "max-h-40 opacity-100"
                        : "max-h-0 opacity-0"
                        }`}
                    >
                      {item.dropdown?.map((sub) => (
                        <Link
                          key={sub.name}
                          to={sub.path!}
                          className={`flex gap-4 items-center  py-1 rounded-md px-2 transition-colors duration-200 ${location.pathname === sub.path
                            ? "text-primary"
                            : "text-gray-700 hover:underline"
                            }`}
                          onClick={() => {
                            if (isMobile() && onClose) onClose(); // close sidebar on mobile
                          }}
                        >
                          {sub.icon} {sub.name}
                        </Link>
                      ))}
                    </div>
                  )}
                </>
              )}
            </div>
          );
        })}
      </nav>
    </aside>
  );
};

export default Sidebar;
