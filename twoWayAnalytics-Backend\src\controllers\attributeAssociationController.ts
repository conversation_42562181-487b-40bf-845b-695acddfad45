import { Request, Response } from "express";
import { sql, poolPromise } from "../config/db";
import dotenv from "dotenv";

dotenv.config();

/**
 * @swagger
 * tags:
 *   name: Attribute Associations
 *   description: API for managing attribute associations
 */

/**
 * Helper function to check if an attribute and entity exist in the database
 */
const checkIfExists = async (publicationId: number, attributeId: number): Promise<boolean> => {
    try {
        const pool = await poolPromise;
        
        const publicationResult = await pool.request()
            .input("publicationId", sql.Int, publicationId)
            .query(`SELECT COUNT(*) as count FROM ${process.env.CLIENT_NAME}.Entity WHERE EntityId = @publicationId AND IsDeleted = 0`);
        
        const attributeResult = await pool.request()
            .input("attributeId", sql.Int, attributeId)
            .query(`SELECT COUNT(*) as count FROM ${process.env.CLIENT_NAME}.Attribute WHERE AttributeId = @attributeId AND IsDeleted = 0`);
        
        return publicationResult.recordset[0].count > 0 && attributeResult.recordset[0].count > 0;
    } catch (error) {
        console.error("Error checking entity/attribute existence:", error);
        return false;
    }
};

/**
 * @swagger
 * /attribute-associations:
 *   post:
 *     summary: Create an attribute association
 *     tags: [Attribute Associations]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               publicationId:
 *                 type: integer
 *               attributeId:
 *                 type: integer
 *     responses:
 *       201:
 *         description: Association created successfully
 *       400:
 *         description: Missing required fields or invalid data
 *       500:
 *         description: Server error
 */
export const addAttributeAssociation = async (req: Request, res: Response): Promise<void> => {
    const { publicationId, attributeId } = req.body;
    if (!publicationId || !attributeId) {
        res.status(400).json({ error: "Both publicationId and attributeId are required" });
        return;
    }
    
    try {
        const exists = await checkIfExists(publicationId, attributeId);
        if (!exists) {
            res.status(400).json({ error: "Invalid publicationId or attributeId. Record not found." });
            return;
        }

        const pool = await poolPromise;
        await pool.request()
            .input("publicationId", sql.Int, publicationId)
            .input("attributeId", sql.Int, attributeId)
            .query(`INSERT INTO ${process.env.CLIENT_NAME}.AttributeAssociation (PublicationId, AttributeId) VALUES (@publicationId, @attributeId)`);
        
        res.status(201).json({ message: "Association created successfully" });
    } catch (err) {
        console.error("Error adding attribute association:", err);
        res.status(500).json({ error: "Server error", details: (err as Error).message });
    }
};

/**
 * @swagger
 * /attribute-associations:
 *   delete:
 *     summary: Delete an attribute association
 *     tags: [Attribute Associations]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               publicationId:
 *                 type: integer
 *               attributeId:
 *                 type: integer
 *     responses:
 *       200:
 *         description: Association deleted successfully
 *       400:
 *         description: Missing required fields or invalid data
 *       500:
 *         description: Server error
 */
export const removeAttributeAssociation = async (req: Request, res: Response): Promise<void> => {
    const { publicationId, attributeId } = req.body;

    if (!publicationId || !attributeId) {
        res.status(400).json({ error: "Both publicationId and attributeId are required" });
        return;
    }

    try {
        const exists = await checkIfExists(publicationId, attributeId);
        if (!exists) {
            res.status(400).json({ error: "Invalid publicationId or attributeId. Record not found." });
            return;
        }

        const pool = await poolPromise;
        const transaction = pool.transaction();
        await transaction.begin();

        await transaction.request()
            .input("publicationId", sql.Int, publicationId)
            .input("attributeId", sql.Int, attributeId)
            .query(`DELETE FROM ${process.env.CLIENT_NAME}.AttributeAssociation WHERE PublicationId = @publicationId AND AttributeId = @attributeId`);

        await transaction.commit(); 
        res.json({ message: "Association deleted successfully" });

    } catch (err) {
        console.error("Error removing attribute association:", err);
        res.status(500).json({ error: "Server error", details: (err as Error).message });
    }
};