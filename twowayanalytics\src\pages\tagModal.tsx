import React, { useEffect, useState } from "react";
import apiService from "../services/apiService";
import { CirclePlus, CircleX } from "lucide-react";

interface TagModalProps {
  isOpen: boolean;
  tagInput: string;
  onChangeInput: (value: string) => void;
  onAddTag: () => void;
  onClose: () => void;
  publicationId?: string;
}

interface TagAssociation {
  AttributeId: number;
  Key: string;
  Value: string;
}

const TagModal: React.FC<TagModalProps> = ({
  isOpen,
  tagInput,
  onChangeInput,
  onAddTag,
  onClose,
  publicationId,
}) => {
  const [tagAssociations, setTagAssociations] = useState<TagAssociation[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (publicationId) {
      fetchAllTags();
    }
  }, [publicationId]);

  const fetchAllTags = async () => {
    if (!publicationId) return;
    setLoading(true);
    try {
      const response = await apiService.get<{ associations: TagAssociation[] }>(
        `/attribute-associations/publication-attribute-association?publicationId=${publicationId}`
      );
      if (response?.associations) {
        const filtered = response.associations.filter(
          (assoc) => assoc.Key === "Tags"
        );
        setTagAssociations(filtered);
      }
    } catch (error) {
      console.error("Error fetching tags:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddTag = async () => {
    if (tagInput.trim() !== "" && publicationId) {
      try {
        await apiService.post("/attribute-associations/tags/associate", {
          tag: tagInput,
          publicationId,
        });
        onAddTag();
        onChangeInput("");
        fetchAllTags();
      } catch (error) {
        console.error("Error adding tag:", error);
      }
    }
  };

  const handleRemoveTag = async (attributeId: number) => {
    if (!publicationId) return;
    try {
      await apiService.deleteattribute(
        `attribute-associations`,{
          attributeId,
          publicationId,
        }
      );
      setTagAssociations((prev) =>
        prev.filter((tag) => tag.AttributeId !== attributeId)
      );
    } catch (error) {
      console.error("Error removing tag:", error);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-[#00000052] bg-opacity-50 z-1000 flex items-center justify-center">
      <div className="bg-white pt-4 pb-10 px-6 rounded-3xl min-w-md relative xs:max-sm:min-w-[95%] xs:max-sm:w-[95%]">
      <div className="flex justify-between items-center">
          <h2 className="text-[21px] font-semibold tracking-tight noto-sans">
            Add Tags
          </h2>
          <CircleX size={28} onClick={onClose} className="cursor-pointer" />
        </div>
        <div className="flex gap-2 mt-6">
          <input
            type="text"
            value={tagInput}
            onChange={(e) => onChangeInput(e.target.value)}
            className="border border-gray-200 rounded-lg px-3 py-2 w-full utility-class outline-none"
            placeholder="Enter tag"
          />
          <button
            className="bg-primary flex items-center py-3 px-4 justify-center gap-2 btn text-white text-md font-extralight rounded-xl"
            onClick={handleAddTag}
          >
            <CirclePlus className="font-lighter" size={20} /> Add
          </button>
        </div>

        {loading ? (
          <p className="text-sm text-gray-500">Loading tags...</p>
        ) : (
          <div className="flex flex-wrap gap-2 mt-4 mb-4">
            {tagAssociations.map((tag) => (
              <div
                key={tag.AttributeId}
                className="bg-lightblue px-3 py-1 rounded-md flex items-center space-x-2"
              >
                <span className="text-blue-500 font-light">{tag.Value}</span>
                <button
                  onClick={() => handleRemoveTag(tag.AttributeId)}
                  className="text-blue-500 text-xl cursor-pointer"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default TagModal;