// import React, { useEffect, useState, useMemo } from "react"; // Import useMemo
// import { useTranslation } from "react-i18next";
// import { FaFacebookF, FaGlobe, FaInstagram, FaXTwitter } from "react-icons/fa6";
// import { GoPencil } from "react-icons/go";
// import { IoIosSearch } from "react-icons/io";
// import { MdFilterList } from "react-icons/md";
// import { RiYoutubeFill } from "react-icons/ri";
// import linkedinIcon from "../assets/Linkedin.svg";
// import thumbnail from "../assets/noImage.png";
// import Dropdown from "../components/Dropdown";
// import Pagination from "../components/Pagination";
// import SocialNetworkChips from "../components/SocialNetworkChips";
// import apiService from "../services/apiService";
// import AddModal from "./addModal";
// import Editmodal from "./editmodal";
// import Loader from "./loader";
// import { useNavigate } from "react-router-dom";
// import { FaEye } from "react-icons/fa";

// function Alerts() {
//   const { t } = useTranslation();
//   const [isModalOpen, setIsModalOpen] = useState(false);
//   const [isAddModalOpen, setIsAddModalOpen] = useState(false);
//   const [selectedPost, setSelectedPost] = useState<any>(null);
//   const [data, setData] = useState<any[]>([]);
//   const [originalData, setOriginalData] = useState<any[]>([]);
//   const [loading, setLoading] = useState(false);
//   const [dateFilter, setDateFilter] = useState(() => localStorage.getItem("alertsDateFilter") || "monthly");
//   const [startDate, setStartDate] = useState(() => {
//     const today = new Date();
//     const lastMonth = new Date(today);
//     lastMonth.setDate(today.getDate() - 29);
//     lastMonth.setHours(0, 0, 0, 0);
//     return lastMonth.toISOString().split("T")[0];
//   });
//   const [endDate, setEndDate] = useState(() => {
//     const today = new Date();
//     today.setHours(23, 59, 59, 999);
//     return today.toISOString().split("T")[0];
//   });
//   const [postType, setPostType] = useState(() => localStorage.getItem("alertsPostType") || "");
//   const [socialNetwork, setSocialNetwork] = useState<string[]>(() =>
//     JSON.parse(localStorage.getItem("alertsSocialNetwork") || "[]")
//   );
//   const [searchTerm, setSearchTerm] = useState(() => localStorage.getItem("alertsSearchTerm") || "");
//   const [sortField, setSortField] = useState<"content" | "endDate" | "status" | "scope" | "">(() =>
//     localStorage.getItem("alertsSortField") as "content" | "endDate" | "status" | "scope" | "" || "endDate"
//   );
//   const [sortDirection, setSortDirection] = useState<"asc" | "desc">(() =>
//     localStorage.getItem("alertsSortDirection") as "asc" | "desc" || "desc"
//   );
//   const [itemsPerPage] = useState(10);
//   const [currentPage, setCurrentPage] = useState(() => {
//     const savedPage = localStorage.getItem("alertsCurrentPage");
//     return savedPage ? parseInt(savedPage, 10) : 1;
//   });
//   const [scopeFilter, setScopeFilter] = useState(() => localStorage.getItem("alertsScopeFilter") || "");

//   const navigate = useNavigate();

//   // Save filter states to localStorage
//   useEffect(() => {
//     localStorage.setItem("alertsDateFilter", dateFilter);
//   }, [dateFilter]);

//   useEffect(() => {
//     localStorage.setItem("alertsStartDate", startDate);
//   }, [startDate]);

//   useEffect(() => {
//     localStorage.setItem("alertsEndDate", endDate);
//   }, [endDate]);

//   useEffect(() => {
//     localStorage.setItem("alertsPostType", postType);
//   }, [postType]);

//   useEffect(() => {
//     localStorage.setItem("alertsSocialNetwork", JSON.stringify(socialNetwork));
//   }, [socialNetwork]);

//   useEffect(() => {
//     localStorage.setItem("alertsSearchTerm", searchTerm);
//   }, [searchTerm]);

//   useEffect(() => {
//     localStorage.setItem("alertsSortField", sortField);
//   }, [sortField]);

//   useEffect(() => {
//     localStorage.setItem("alertsSortDirection", sortDirection);
//   }, [sortDirection]);

//   useEffect(() => {
//     localStorage.setItem("alertsCurrentPage", currentPage.toString());
//   }, [currentPage]);

//   useEffect(() => {
//     localStorage.setItem("alertsScopeFilter", scopeFilter);
//   }, [scopeFilter]);

//   // Validate currentPage when data changes
//   useEffect(() => {
//     const maxPage = Math.ceil(data.length / itemsPerPage);
//     if (currentPage > maxPage && maxPage > 0) {
//       setCurrentPage(1);
//       localStorage.setItem("alertsCurrentPage", "1");
//     }
//   }, [data, currentPage, itemsPerPage]);

//   // Fetch initial data on mount
//   useEffect(() => {
//     fetchFilteredData();
//   }, []);

//   // Apply search whenever searchTerm changes
//   useEffect(() => {
//     if (searchTerm !== "") {
//       const filtered = originalData.filter((alert) => {
//         const searchLower = searchTerm.toLowerCase();
//         return (
//           alert.Content?.toLowerCase().includes(searchLower) ||
//           alert.Channel?.toLowerCase().includes(searchLower) ||
//           alert.MetricType?.toLowerCase().includes(searchLower) ||
//           alert.Metrics?.toLowerCase().includes(searchLower) ||
//           alert.Scope?.toLowerCase().includes(searchLower)
//         );
//       });
//       setData(filtered);
//       setCurrentPage(1);
//       localStorage.setItem("alertsCurrentPage", "1");
//     } else {
//       setData(originalData);
//     }
//   }, [originalData, searchTerm]);

//   const fetchFilteredData = async () => {
//     setLoading(true);
//     try {
//       const filterParam = dateFilter === "custom" ? "Custom Date" :
//         dateFilter === "daily" ? "Daily Alerts" :
//           dateFilter === "weekly" ? "Last Week Alerts" :
//             dateFilter === "monthly" ? "Last Month Alerts" : "All";

//       let beginDate = startDate;
//       let endDateParam = endDate;

//       if (dateFilter !== "custom") {
//         const today = new Date();
//         today.setHours(23, 59, 59, 999);
//         endDateParam = today.toISOString().split("T")[0];

//         if (dateFilter === "daily") {
//           beginDate = new Date(today).toISOString().split("T")[0];
//         } else if (dateFilter === "weekly") {
//           const lastWeek = new Date(today);
//           lastWeek.setDate(today.getDate() - 6);
//           lastWeek.setHours(0, 0, 0, 0);
//           beginDate = lastWeek.toISOString().split("T")[0];
//         } else if (dateFilter === "monthly") {
//           const lastMonth = new Date(today);
//           lastMonth.setDate(today.getDate() - 29);
//           lastMonth.setHours(0, 0, 0, 0);
//           beginDate = lastMonth.toISOString().split("T")[0];
//         }
//       }

//       const params = new URLSearchParams({
//         Filter: filterParam,
//         Begin: beginDate,
//         End: endDateParam,
//       });
//       const response = await apiService.getlocal<any[]>(`/api/V1/alerts?${params.toString()}`);

//       let processedData = response;
//       if (scopeFilter) {
//         processedData = response.filter(item => item.Scope?.toLowerCase() === scopeFilter.toLowerCase());
//       }

//       setData(processedData);
//       setOriginalData(response);
//     } catch (error) {
//       setData([]);
//       setOriginalData([]);
//       console.error("Error fetching filtered data:", error);
//     } finally {
//       setLoading(false);
//     }
//   };

//   const handleEditClick = (alert: any) => {
//     setSelectedPost(alert);
//     setIsModalOpen(true);

//     const alertId = alert?.id || alert?._id;
//     const metricType = alert?.MetricType;

//     if (alertId) {
//       let routeType;

//       if (metricType === 'KPI') {
//         routeType = 'Metrics';
//       } else if (metricType === 'Mention') {
//         routeType = 'mentions';
//       } else {
//         routeType = metricType;
//       }

//       navigate(`/alerts/${routeType}/${alertId}`);
//     } else {
//       console.warn("No alert ID provided.");
//     }
//   };

//   const handleToggleStatus = async (alert: any) => {
//     if (alert.status) {
//       setLoading(true);
//       try {
//         const response = await apiService.postlocal(`/api/V1/alert/deactivate`, {
//           id: alert.id,
//         });
//         console.log("Status toggle response:", response);
//       } catch (error) {
//         console.error("Error toggling status:", error);
//       } finally {
//         fetchFilteredData();
//         setLoading(false);
//       }
//     }
//   };

//   const handleFilter = () => {
//     fetchFilteredData();
//     setCurrentPage(1);
//     localStorage.setItem("alertsCurrentPage", "1");
//   };

//   const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
//     setSearchTerm(e.target.value);
//   };

//   const handleDateFilterChange = (value: string) => {
//     setDateFilter(value);
//     if (value !== "custom") {
//       const today = new Date();
//       let start: Date;
//       let end: Date = new Date(today);
//       end.setHours(23, 59, 59, 999);

//       if (value === "daily") {
//         start = new Date(today);
//         start.setHours(0, 0, 0, 0);
//       } else if (value === "weekly") {
//         start = new Date(today);
//         start.setDate(today.getDate() - 6);
//         start.setHours(0, 0, 0, 0);
//       } else if (value === "monthly") {
//         start = new Date(today);
//         start.setDate(today.getDate() - 29);
//         start.setHours(0, 0, 0, 0);
//       } else {
//         setStartDate("");
//         setEndDate("");
//         return;
//       }

//       setStartDate(start.toISOString().split("T")[0]);
//       setEndDate(end.toISOString().split("T")[0]);
//     }
//   };

//   const sortData = (field: "content" | "endDate" | "status" | "scope" | "") => {
//     let direction: "asc" | "desc" = field === "endDate" ? "desc" : "asc";
//     if (sortField === field) {
//       direction = sortDirection === "asc" ? "desc" : "asc";
//     }

//     setSortField(field);
//     setSortDirection(direction);

//     if (field === "") {
//       setData([...originalData]);
//       return;
//     }

//     const sortedData = [...data].sort((a, b) => {
//       if (field === "content") {
//         const contentA = a.Content?.toLowerCase() || "";
//         const contentB = b.Content?.toLowerCase() || "";
//         return direction === "asc" ? contentA.localeCompare(contentB) : contentB.localeCompare(contentA);
//       } else if (field === "endDate") {
//         const dateA = new Date(a.EndDate || 0);
//         const dateB = new Date(b.EndDate || 0);
//         return direction === "asc" ? dateA.getTime() - dateB.getTime() : dateB.getTime() - dateA.getTime();
//       } else if (field === "status") {
//         const statusA = a.status ? "true" : "false";
//         const statusB = b.status ? "true" : "false";
//         return direction === "asc" ? statusA.localeCompare(statusB) : statusB.localeCompare(statusA);
//       } else if (field === "scope") {
//         const scopeA = a.Scope?.toLowerCase() || "";
//         const scopeB = b.Scope?.toLowerCase() || "";
//         return direction === "asc" ? scopeA.localeCompare(scopeB) : scopeB.localeCompare(scopeA);
//       }
//       return 0;
//     });

//     setData(sortedData);
//   };

//   // const totalItems = data.length;
//   // const startIndex = (currentPage - 1) * itemsPerPage;
//   // endIndex = Math.min(startIndex + itemsPerPage, totalItems);
//   // const paginatedData = data.slice(startIndex, endIndex);
//   const totalItems = data.length;
//   const startIndex = (currentPage - 1) * itemsPerPage;
//   const endIndex = Math.min(startIndex + itemsPerPage, totalItems);
//   const paginatedData = data.slice(startIndex, endIndex);

//   const handlePageChange = (newPage: number) => {
//     if (newPage >= 1 && newPage <= Math.ceil(data.length / itemsPerPage)) {
//       setCurrentPage(newPage);
//     }
//   };

//   const options = [
//     { value: "", label: "All" },
//     { value: "KPI", label: "KPI" },
//     { value: "Sentiment", label: "Sentiment" },
//     { value: "Mention", label: "Mention" },
//   ];

//   const dateFilterOptions = [
//     { value: "", label: "All" },
//     { value: "daily", label: t("alerts-tablet.daily_posts") },
//     { value: "weekly", label: t("alerts-tablet.last_week_posts") },
//     { value: "monthly", label: t("alerts-tablet.last_month_posts") },
//     { value: "custom", label: t("alerts-tablet.custom_date") },
//   ];

//   // Dynamically generate scopeOptions from originalData
//   const scopeOptions = useMemo(() => {
//     const uniqueScopes = new Set<string>();
//     originalData.forEach(item => {
//       if (item.Scope) {
//         uniqueScopes.add(item.Scope);
//       }
//     });
//     const sortedScopes = Array.from(uniqueScopes).sort((a, b) => a.localeCompare(b));
//     return [{ value: "", label: "All" }, ...sortedScopes.map(scope => ({ value: scope.toLowerCase(), label: scope }))];
//   }, [originalData]);

//   const getSocialNetworkIcon = (channel: string) => {
//     switch (channel.toLowerCase()) {
//       case "facebook":
//         return (
//           <span>
//             <FaFacebookF
//               className="text-[1.60rem] bg-[#1877F2] p-1.5 text-[#fff] rounded-lg"
//               style={{ boxShadow: "0px 3px 10px 0px #1976D252" }}
//             />
//           </span>
//         );
//       case "instagram":
//         return (
//           <span>
//             <FaInstagram
//               className="text-[1.60rem] p-1.5 text-white rounded-lg"
//               style={{
//                 background:
//                   "linear-gradient(45deg, #feda75, #fa7e1e, #d62976, #962fbf, #4f5bd5)",
//                 boxShadow: "0px 4px 4px 0px rgba(0, 0, 0, 0.2)",
//               }}
//             />
//           </span>
//         );
//       case "twitter":
//         return (
//           <span>
//             <FaXTwitter
//               className="text-[1.60rem] bg-[#000] p-1.5 text-[#fff] rounded-lg"
//               style={{ boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.2)" }}
//             />
//           </span>
//         );
//       case "youtube":
//         return (
//           <span>
//             <RiYoutubeFill
//               className="text-[1.60rem] bg-[#E21A20] p-1.5 text-[#fff] rounded-lg"
//               style={{ boxShadow: "0px 4px 4px 0px #E21A204F" }}
//             />
//           </span>
//         );
//       case "linkedin":
//         return (
//           <span>
//             <img src={linkedinIcon} alt="LinkedIn" className="w-6 h-6" />
//           </span>
//         );
//       case "all":
//         return (
//           <span>
//             <FaGlobe
//               className="text-[1.60rem] bg-gradient-to-r from-purple-500 to-blue-500 p-1.5 text-white rounded-lg"
//               style={{ boxShadow: "0px 3px 10px 0px rgba(0, 0, 0, 0.2)" }}
//             />
//           </span>
//         );
//       default:
//         return null;
//     }
//   };

//   const getAlertDisplayColor = (alertDisplay: number | null) => {
//     switch (alertDisplay) {
//       case 1:
//         return "bg-red-100 text-red-600";     // Hot
//       case 2:
//         return "bg-yellow-100 text-yellow-700"; // Warm
//       case 3:
//         return "bg-green-100 text-green-700";  // Cold
//       case null:
//         return "bg-gray-100 text-gray-500";    // No Value
//       default:
//         return "bg-blue-100 text-blue-600";    // Default (like 'Pending')
//     }
//   };

//   return (
//     <div className="bg-white rounded-3xl shadow xs:max-sm:mt-4 pt-4">
//       <div className={`flex flex-col xs:max-sm:px-4 sm:px-4`}>
//         <div className="w-full mb-4">
//           <div className="relative w-full">
//             <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
//               <IoIosSearch className="text-gray-400" />
//             </div>
//             <input
//               type="text"
//               placeholder={t("alerts-tablet.Search Posts") || "Search alerts..."}
//               className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-3xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
//               value={searchTerm}
//               onChange={handleSearch}
//             />
//           </div>
//         </div>

//         <div className="flex flex-col sm:flex-row sm:flex-wrap gap-4 w-full">
//           <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px]">
//             <label htmlFor="date-filter" className="text-[#5A5A5A] mb-2 block">
//               {t("alerts-tablet.date_filter")}
//             </label>
//             <Dropdown
//               value={dateFilter}
//               onChange={handleDateFilterChange}
//               options={dateFilterOptions}
//               placeholder="Select time frame"
//               className="rounded-3xl h-[47px] w-full"
//             />
//           </div>

//           {dateFilter === "custom" && (
//             <>
//               <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px]">
//                 <label htmlFor="start-date" className="text-[#5A5A5A] mb-2 block">
//                   {t("alerts-tablet.begin")}
//                 </label>
//                 <input
//                   id="start-date"
//                   type="date"
//                   value={startDate}
//                   onChange={(e) => setStartDate(e.target.value)}
//                   className="w-full rounded-lg border border-gray-200 bg-white px-3 h-[47px] py-2 text-gray-900"
//                 />
//               </div>

//               <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px]">
//                 <label htmlFor="end-date" className="text-[#5A5A5A] mb-2 block">
//                   {t("alerts-tablet.end")}
//                 </label>
//                 <input
//                   id="end-date"
//                   type="date"
//                   value={endDate}
//                   onChange={(e) => setEndDate(e.target.value)}
//                   className="w-full rounded-lg border border-gray-200 bg-white px-3 h-[47px] py-2 text-gray-900"
//                 />
//               </div>
//             </>
//           )}

//           <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px]">
//             <label htmlFor="type" className="text-[#5A5A5A] mb-2 block">
//               {t("alerts-tablet.type")}
//             </label>
//             <Dropdown
//               value={postType}
//               onChange={setPostType}
//               options={options}
//               placeholder="Select metric type"
//               className="rounded-3xl h-[47px] w-full"
//             />
//           </div>

//           <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px]">
//             <label htmlFor="scope-filter" className="text-[#5A5A5A] mb-2 block">
//               {t("alerts-tablet.scope")}
//             </label>
//             <Dropdown
//               value={scopeFilter}
//               onChange={setScopeFilter}
//               options={scopeOptions}
//               placeholder="Select scope"
//               className="rounded-3xl h-[47px] w-full"
//             />
//           </div>

//           <div className="w-full sm:flex-1 flex flex-col sm:flex-row items-end gap-4 mt-2 sm:mt-0">
//             <div className="w-full sm:flex-1">
//               <SocialNetworkChips
//                 setSocialNetwork={setSocialNetwork}
//                 socialNetwork={socialNetwork}
//                 t={t}
//               />
//             </div>
//             <div className="w-full sm:w-auto flex flex-col sm:flex-row gap-4">
//               <button
//                 onClick={handleFilter}
//                 className="bg-secondary w-full sm:w-auto flex items-center justify-center gap-2 btn text-[#ffffff] py-2 px-10 text-lg h-[47px] font-light rounded-xl"
//               >
//                 <MdFilterList />
//                 {t("alerts-tablet.button")}
//               </button>

//               <button
//                 onClick={() => navigate("/alerts/sentiment")}
//                 className="bg-secondary w-full sm:w-auto flex items-center justify-center gap-2 btn text-[#ffffff] py-2 px-10 text-lg h-[47px] font-light rounded-xl"
//               >
//                 <MdFilterList />
//                 {t("Add Sentiments")}
//               </button>
//             </div>
//           </div>
//         </div>
//       </div>

//       <div className="mt-6 rounded-xl xs:max-sm:hidden">
//         <div className="bg-white overflow-hidden">
//           <div className="overflow-x-auto relative">
//             <table className="min-w-full border-collapse text-left text-sm">
//               <thead>
//                 <tr className="border-b border-gray-200 font-bold tracking-tight bg-[#EDF1F575] text-sm">
//                   <th className="left-col">{t("alerts-tablet.thumbnail")}</th>
//                   <th className="p-4" onClick={() => sortData("content")}>
//                     <div className="flex items-center">
//                       {t("alerts-tablet.content")}
//                       <span className="ml-1 text-xs leading-none pl-1">
//                         {sortField === "content" ? (sortDirection === "asc" ? "↑" : "↓") : "↑↓"}
//                       </span>
//                     </div>
//                   </th>
//                   <th className="p-4">{t("alerts-tablet.channel")}</th>
//                   <th className="p-4" onClick={() => sortData("endDate")}>
//                     <div className="flex items-center">
//                       {t("alerts-tablet.endDate")}
//                       <span className="ml-1 text-xs leading-none pl-1">
//                         {sortField === "endDate" ? (sortDirection === "asc" ? "↑" : "↓") : "↑↓"}
//                       </span>
//                     </div>
//                   </th>
//                   <th className="p-4">{t("alerts-tablet.metricType")}</th>
//                   <th className="p-4" onClick={() => sortData("scope")}>
//                     <div className="flex items-center">
//                       {t("alerts-tablet.scope")}
//                       <span className="ml-1 text-xs leading-none pl-1">
//                         {sortField === "scope" ? (sortDirection === "asc" ? "↑" : "↓") : "↑↓"}
//                       </span>
//                     </div>
//                   </th>
//                   <th className="p-4">{t("alerts-tablet.alert_count")}</th>
//                   <th className="p-4">{t("alerts-tablet.metrics")}</th>
//                   <th className="p-4" onClick={() => sortData("status")}>
//                     <div className="flex items-center">
//                       {t("alerts-tablet.status")}
//                       <span className="ml-1 text-xs leading-none pl-1">
//                         {sortField === "status" ? (sortDirection === "asc" ? "↑" : "↓") : "↑↓"}
//                       </span>
//                     </div>
//                   </th>
//                   <th className="right-col">{t("alerts-tablet.actions")}</th>
//                 </tr>
//               </thead>
//               <tbody>
//                 {loading ? (
//                   <tr>
//                     <td colSpan={10}>
//                       <Loader />
//                     </td>
//                   </tr>
//                 ) : paginatedData.length > 0 ? (
//                   paginatedData.map((item: any) => (
//                     <tr key={item.id} className="border-b border-gray-100 hover:bg-gray-50">
//                       <td className="left-col relative">
//                         {item["Image URL"] && item["Image URL"] !== "NA" ? (
//                           <img
//                             src={item["Image URL"]}
//                             alt="Thumbnail"
//                             className="w-10 h-10 rounded-xl"
//                           />
//                         ) : (
//                           <img
//                             src={thumbnail}
//                             alt="Default Thumbnail"
//                             className="w-10 h-10 rounded-xl"
//                           />
//                         )}
//                         {/* {item.AlertDisplay && (
//                           <span
//                             className={`absolute top-1 right-1 w-2 h-2 rounded-full ${getAlertDisplayColor(item.AlertDisplay)}`}
//                           ></span>
//                         )} */}
//                       </td>

//                       <td
//                         className={`p-4 text-[0.85rem] text-nowrap truncate max-w-[150px] lg:min-w-[280px] ${getAlertDisplayColor(item.AlertDisplay)}`}
//                         title={item.Content || "N/A"}
//                       >
//                         {item.Content || "N/A"}
//                       </td>
//                       <td className="p-4 min-w-[100px]">
//                         <div className="flex items-center space-x-0.5 flex-wrap">
//                           {getSocialNetworkIcon(item.Channel)}
//                         </div>
//                       </td>
//                       <td className="p-4 text-[0.85rem]">
//                         {new Date(item.EndDate).toLocaleDateString()}
//                       </td>
//                       <td className="p-4 text-[0.85rem]">
//                         {item.MetricType || "N/A"}
//                       </td>
//                       <td className="p-4 text-[0.85rem]">
//                         {item.Scope || "N/A"}
//                       </td>
//                       <td className="p-4 text-[0.85rem]">
//                         {item["Alert Count"] ?? "N/A"}
//                       </td>
//                       <td className="p-4 text-[0.85rem]">
//                         {item.Metrics || "N/A"}
//                       </td>
//                       <td className="p-4">
//                         <button
//                           disabled={!item.status}
//                           className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none ${item.status ? "bg-primary cursor-pointer" : "bg-gray-300 cursor-not-allowed"
//                             }`}
//                           onClick={() => handleToggleStatus(item)}
//                         >
//                           <span
//                             className={`inline-block h-4 w-4 transform bg-white rounded-full transition-transform ${item.status ? "translate-x-6" : "translate-x-1"
//                               }`}
//                           />
//                         </button>
//                       </td>
//                       <td className="right-col">
//                         <div className="flex items-center gap-1">
//                           <button
//                             className="px-2 py-2 rounded-lg bg-[#21CE9E1A] cursor-pointer text-sm text-[#21CE9E]"
//                             onClick={() => handleEditClick(item)}
//                           >
//                             <GoPencil size={16} />
//                           </button>
//                           <button
//                             className="px-2 py-2 rounded-lg bg-[#21CE9E1A] cursor-pointer text-sm text-[#21CE9E]"
//                             onClick={() => handleEditClick(item)}
//                           >
//                             <FaEye />
//                           </button>
//                         </div>
//                       </td>
//                     </tr>
//                   ))
//                 ) : (
//                   <tr>
//                     <td colSpan={10} className="text-center py-4">
//                       No data available
//                     </td>
//                   </tr>
//                 )}
//               </tbody>
//             </table>
//             {!loading && (
//               <div className="mt-4 px-8">
//                 <Pagination
//                   currentPage={currentPage}
//                   totalItems={data.length}
//                   itemsPerPage={itemsPerPage}
//                   onPageChange={handlePageChange}
//                 />
//               </div>
//             )}
//           </div>
//         </div>
//       </div>

//       {/* MOBILE UI */}
//       <div className="mt-6 rounded-xl hidden xs:max-sm:block">
//         <div className="bg-white overflow-hidden">
//           <div className="overflow-x-auto relative">
//             <table className="min-w-full border-collapse text-left text-sm">
//               <thead>
//                 <tr className="border-b border-gray-200 font-bold tracking-tight bg-[#EDF1F575] text-sm">
//                   <th className="px-8 py-5">{t("alerts-tablet.thumbnail")}</th>
//                 </tr>
//               </thead>
//               <tbody className="flex flex-col">
//                 {loading ? (
//                   <tr>
//                     <td colSpan={7}>
//                       <Loader />
//                     </td>
//                   </tr>
//                 ) : paginatedData.length > 0 ? (
//                   paginatedData.map((item: any) => (
//                     <tr key={item.id} className="border-b border-gray-100 hover:bg-gray-50">
//                       <td className="py-4 px-8 flex flex-col gap-2 w-full">
//                         <div className="flex justify-between items-center">
//                           <div className="relative">
//                             {item["Image URL"] !== "NA" ? (
//                               <img
//                                 src={item["Image URL"] || thumbnail}
//                                 alt="Thumbnail"
//                                 className="w-15 h-15 rounded-xl"
//                               />
//                             ) : (
//                               <img
//                                 src={thumbnail}
//                                 alt="Default Thumbnail"
//                                 className="w-15 h-15 rounded-xl"
//                               />
//                             )}
//                             {item.AlertDisplay && (
//                               <span
//                                 className={`absolute top-1 right-1 w-2 h-2 rounded-full ${getAlertDisplayColor(item.AlertDisplay)}`}
//                               ></span>
//                             )}
//                           </div>
//                           <div className="flex gap-2">
//                             <button
//                               className="px-2 py-2 rounded-lg mt-3.5 text-md bg-[#21CE9E1A] cursor-pointer text-xl text-[#21CE9E]"
//                               onClick={() => handleEditClick(item)}
//                             >
//                               <GoPencil />
//                             </button>
//                           </div>
//                         </div>
//                         <div className="flex justify-between items-center mt-2">
//                           <div className="flex items-center space-x-2">
//                             {getSocialNetworkIcon(item.Channel)}
//                           </div>
//                           <div className="flex items-center gap-2">
//                             <span className="py-4 px-8 text-sm">
//                               {new Date(item.EndDate).toLocaleDateString()}
//                             </span>
//                             <button
//                               disabled={!item.status}
//                               className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none ${item.status ? "bg-primary cursor-pointer" : "bg-gray-300 cursor-not-allowed"
//                                 }`}
//                               onClick={() => handleToggleStatus(item)}
//                             >
//                               <span
//                                 className={`inline-block h-4 w-4 transform bg-white rounded-full transition-transform ${item.status ? "translate-x-6" : "translate-x-1"
//                                   }`}
//                               />
//                             </button>
//                           </div>
//                         </div>
//                         <div className={`py-4 px-8 max-w-[200px] text-sm text-nowrap truncate ${getAlertDisplayColor(item.AlertDisplay)}`}>
//                           {item.Content}
//                         </div>
//                         <div className="py-4 px-8 text-sm">
//                           <strong>{t("alerts-tablet.scope")}:</strong> {item.Scope || "N/A"}
//                         </div>
//                         <div className="py-4 px-8 text-sm">
//                           <strong>{t("alerts-tablet.alert_count")}:</strong> {item["Alert Count"] ?? "N/A"}
//                         </div>
//                       </td>
//                     </tr>
//                   ))
//                 ) : (
//                   <tr>
//                     <td colSpan={7} className="text-center py-4">
//                       No data available
//                     </td>
//                   </tr>
//                 )}
//               </tbody>
//             </table>
//             {!loading && (
//               <div className="mt-4 px-8">
//                 <Pagination
//                   currentPage={currentPage}
//                   totalItems={data.length}
//                   itemsPerPage={itemsPerPage}
//                   onPageChange={handlePageChange}
//                 />
//               </div>
//             )}
//           </div>
//         </div>
//       </div>

//       {isModalOpen && (
//         <Editmodal
//           isOpen={isModalOpen}
//           onClose={() => setIsModalOpen(false)}
//           onOpenAddModal={() => {
//             setIsModalOpen(false);
//             setIsAddModalOpen(true);
//           }}
//           selectedPost={selectedPost}
//           refreshData={() => fetchFilteredData()}
//         />
//       )}
//       {isAddModalOpen && (
//         <AddModal
//           isOpen={isAddModalOpen}
//           onClose={() => setIsAddModalOpen(false)}
//           selectedPost={selectedPost}
//           data={data}
//           refreshData={() => fetchFilteredData()}
//         />
//       )}
//     </div>
//   );
// }

// export default Alerts;


import React, { useEffect, useState, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { FaFacebookF, FaGlobe, FaInstagram, FaXTwitter } from "react-icons/fa6";
import { GoPencil } from "react-icons/go";
import { IoIosSearch } from "react-icons/io";
import { MdFilterList } from "react-icons/md";
import { RiYoutubeFill } from "react-icons/ri";
import linkedinIcon from "../assets/Linkedin.svg";
import thumbnail from "../assets/noImage.png";
import Dropdown from "../components/Dropdown";
import Pagination from "../components/Pagination";
import SocialNetworkChips from "../components/SocialNetworkChips";
import apiService from "../services/apiService";
import AddModal from "./addModal";
import Editmodal from "./editmodal";
import Loader from "./loader";
import { useNavigate } from "react-router-dom";
import { FaEye } from "react-icons/fa";

function Alerts() {
  const { t } = useTranslation();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [selectedPost, setSelectedPost] = useState<any>(null);
  const [data, setData] = useState<any[]>([]);
  const [originalData, setOriginalData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [dateFilter, setDateFilter] = useState(() => localStorage.getItem("alertsDateFilter") || "monthly");
  const [startDate, setStartDate] = useState(() => {
    const today = new Date();
    const lastMonth = new Date(today);
    lastMonth.setDate(today.getDate() - 29);
    lastMonth.setHours(0, 0, 0, 0);
    return lastMonth.toISOString().split("T")[0];
  });
  const [endDate, setEndDate] = useState(() => {
    const today = new Date();
    today.setHours(23, 59, 59, 999);
    return today.toISOString().split("T")[0];
  });
  const [postType, setPostType] = useState(() => localStorage.getItem("alertsPostType") || "");
  const [socialNetwork, setSocialNetwork] = useState<string[]>(() =>
    JSON.parse(localStorage.getItem("alertsSocialNetwork") || "[]")
  );
  const [searchTerm, setSearchTerm] = useState(() => localStorage.getItem("alertsSearchTerm") || "");
  const [sortField, setSortField] = useState<"content" | "endDate" | "status" | "scope" | "">(() =>
    localStorage.getItem("alertsSortField") as "content" | "endDate" | "status" | "scope" | "" || "endDate"
  );
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">(() =>
    localStorage.getItem("alertsSortDirection") as "asc" | "desc" || "desc"
  );
  const [itemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(() => {
    const savedPage = localStorage.getItem("alertsCurrentPage");
    return savedPage ? parseInt(savedPage, 10) : 1;
  });
  const [scopeFilter, setScopeFilter] = useState(() => localStorage.getItem("alertsScopeFilter") || "");

  const navigate = useNavigate();

  // Save filter states to localStorage
  useEffect(() => {
    localStorage.setItem("alertsDateFilter", dateFilter);
  }, [dateFilter]);

  useEffect(() => {
    localStorage.setItem("alertsStartDate", startDate);
  }, [startDate]);

  useEffect(() => {
    localStorage.setItem("alertsEndDate", endDate);
  }, [endDate]);

  useEffect(() => {
    localStorage.setItem("alertsPostType", postType);
  }, [postType]);

  useEffect(() => {
    localStorage.setItem("alertsSocialNetwork", JSON.stringify(socialNetwork));
  }, [socialNetwork]);

  useEffect(() => {
    localStorage.setItem("alertsSearchTerm", searchTerm);
  }, [searchTerm]);

  useEffect(() => {
    localStorage.setItem("alertsSortField", sortField);
  }, [sortField]);

  useEffect(() => {
    localStorage.setItem("alertsSortDirection", sortDirection);
  }, [sortDirection]);

  useEffect(() => {
    localStorage.setItem("alertsCurrentPage", currentPage.toString());
  }, [currentPage]);

  useEffect(() => {
    localStorage.setItem("alertsScopeFilter", scopeFilter);
  }, [scopeFilter]);

  // Validate currentPage when data changes
  useEffect(() => {
    const maxPage = Math.ceil(data.length / itemsPerPage);
    if (currentPage > maxPage && maxPage > 0) {
      setCurrentPage(1);
      localStorage.setItem("alertsCurrentPage", "1");
    }
  }, [data, currentPage, itemsPerPage]);

  // Fetch initial data on mount with only date filter
  useEffect(() => {
    fetchFilteredData();
  }, []);

  // Apply frontend filters whenever they change
  useEffect(() => {
    applyFrontendFilters();
  }, [originalData, searchTerm, postType, socialNetwork, scopeFilter]);

  const applyFrontendFilters = () => {
    let filtered = [...originalData];

    // Apply search filter
    if (searchTerm !== "") {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter((alert) => {
        return (
          alert.Content?.toLowerCase().includes(searchLower) ||
          alert.Channel?.toLowerCase().includes(searchLower) ||
          alert.MetricType?.toLowerCase().includes(searchLower) ||
          alert.Metrics?.toLowerCase().includes(searchLower) ||
          alert.Scope?.toLowerCase().includes(searchLower)
        );
      });
    }

    // Apply post type filter
    if (postType) {
      filtered = filtered.filter(alert => alert.MetricType === postType);
    }

    // Apply social network filter
    if (socialNetwork.length > 0) {
      filtered = filtered.filter(alert => socialNetwork.includes(alert.Channel));
    }

    // Apply scope filter
    if (scopeFilter) {
      filtered = filtered.filter(alert => alert.Scope?.toLowerCase() === scopeFilter.toLowerCase());
    }

    setData(filtered);
    setCurrentPage(1);
    localStorage.setItem("alertsCurrentPage", "1");
  };

  const fetchFilteredData = async () => {
    setLoading(true);
    try {
      const filterParam = dateFilter === "custom" ? "Custom Date" :
        dateFilter === "daily" ? "Daily Alerts" :
          dateFilter === "weekly" ? "Last Week Alerts" :
            dateFilter === "monthly" ? "Last Month Alerts" : "All";

      let beginDate = startDate;
      let endDateParam = endDate;

      if (dateFilter !== "custom") {
        const today = new Date();
        today.setHours(23, 59, 59, 999);
        endDateParam = today.toISOString().split("T")[0];

        if (dateFilter === "daily") {
          beginDate = new Date(today).toISOString().split("T")[0];
        } else if (dateFilter === "weekly") {
          const lastWeek = new Date(today);
          lastWeek.setDate(today.getDate() - 6);
          lastWeek.setHours(0, 0, 0, 0);
          beginDate = lastWeek.toISOString().split("T")[0];
        } else if (dateFilter === "monthly") {
          const lastMonth = new Date(today);
          lastMonth.setDate(today.getDate() - 29);
          lastMonth.setHours(0, 0, 0, 0);
          beginDate = lastMonth.toISOString().split("T")[0];
        }
      }

      const params = new URLSearchParams({
        Filter: filterParam,
        Begin: beginDate,
        End: endDateParam,
      });
      const response = await apiService.getlocal<any[]>(`/api/V1/alerts?${params.toString()}`);

      setOriginalData(response);
    } catch (error) {
      setData([]);
      setOriginalData([]);
      console.error("Error fetching filtered data:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleEditClick = (alert: any) => {
    setSelectedPost(alert);
    setIsModalOpen(true);

    const alertId = alert?.id || alert?._id;
    const metricType = alert?.MetricType;

    if (alertId) {
      let routeType;

      if (metricType === 'KPI') {
        routeType = 'Metrics';
      } else if (metricType === 'Mention') {
        routeType = 'mentions';
      } else {
        routeType = metricType;
      }

      navigate(`/alerts/${routeType}/${alertId}`);
    } else {
      console.warn("No alert ID provided.");
    }
  };

  const handleToggleStatus = async (alert: any) => {
    if (alert.status) {
      setLoading(true);
      try {
        const response = await apiService.postlocal(`/api/V1/alert/deactivate`, {
          id: alert.id,
        });
        console.log("Status toggle response:", response);
      } catch (error) {
        console.error("Error toggling status:", error);
      } finally {
        fetchFilteredData();
        setLoading(false);
      }
    }
  };

  const handleFilter = () => {
    fetchFilteredData();
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleDateFilterChange = (value: string) => {
    setDateFilter(value);
    if (value !== "custom") {
      const today = new Date();
      let start: Date;
      let end: Date = new Date(today);
      end.setHours(23, 59, 59, 999);

      if (value === "daily") {
        start = new Date(today);
        start.setHours(0, 0, 0, 0);
      } else if (value === "weekly") {
        start = new Date(today);
        start.setDate(today.getDate() - 6);
        start.setHours(0, 0, 0, 0);
      } else if (value === "monthly") {
        start = new Date(today);
        start.setDate(today.getDate() - 29);
        start.setHours(0, 0, 0, 0);
      } else {
        setStartDate("");
        setEndDate("");
        return;
      }

      setStartDate(start.toISOString().split("T")[0]);
      setEndDate(end.toISOString().split("T")[0]);
    }
  };

  const sortData = (field: "content" | "endDate" | "status" | "scope" | "") => {
    let direction: "asc" | "desc" = field === "endDate" ? "desc" : "asc";
    if (sortField === field) {
      direction = sortDirection === "asc" ? "desc" : "asc";
    }

    setSortField(field);
    setSortDirection(direction);

    if (field === "") {
      setData([...originalData]);
      return;
    }

    const sortedData = [...data].sort((a, b) => {
      if (field === "content") {
        const contentA = a.Content?.toLowerCase() || "";
        const contentB = b.Content?.toLowerCase() || "";
        return direction === "asc" ? contentA.localeCompare(contentB) : contentB.localeCompare(contentA);
      } else if (field === "endDate") {
        const dateA = new Date(a.EndDate || 0);
        const dateB = new Date(b.EndDate || 0);
        return direction === "asc" ? dateA.getTime() - dateB.getTime() : dateB.getTime() - dateA.getTime();
      } else if (field === "status") {
        const statusA = a.status ? "true" : "false";
        const statusB = b.status ? "true" : "false";
        return direction === "asc" ? statusA.localeCompare(statusB) : statusB.localeCompare(statusA);
      } else if (field === "scope") {
        const scopeA = a.Scope?.toLowerCase() || "";
        const scopeB = b.Scope?.toLowerCase() || "";
        return direction === "asc" ? scopeA.localeCompare(scopeB) : scopeB.localeCompare(scopeA);
      }
      return 0;
    });

    setData(sortedData);
  };

  const totalItems = data.length;
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, totalItems);
  const paginatedData = data.slice(startIndex, endIndex);

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= Math.ceil(data.length / itemsPerPage)) {
      setCurrentPage(newPage);
    }
  };

  const options = [
    { value: "", label: "All" },
    { value: "KPI", label: "KPI" },
    { value: "Sentiment", label: "Sentiment" },
    { value: "Mention", label: "Mention" },
  ];

  const dateFilterOptions = [
    { value: "", label: "All" },
    { value: "daily", label: t("alerts-tablet.daily_posts") },
    { value: "weekly", label: t("alerts-tablet.last_week_posts") },
    { value: "monthly", label: t("alerts-tablet.last_month_posts") },
    { value: "custom", label: t("alerts-tablet.custom_date") },
  ];

  // Dynamically generate scopeOptions from originalData
  const scopeOptions = useMemo(() => {
    const uniqueScopes = new Set<string>();
    originalData.forEach(item => {
      if (item.Scope) {
        uniqueScopes.add(item.Scope);
      }
    });
    const sortedScopes = Array.from(uniqueScopes).sort((a, b) => a.localeCompare(b));
    return [{ value: "", label: "All" }, ...sortedScopes.map(scope => ({ value: scope.toLowerCase(), label: scope }))];
  }, [originalData]);

  const getSocialNetworkIcon = (channel: string) => {
    switch (channel.toLowerCase()) {
      case "facebook":
        return (
          <span>
            <FaFacebookF
              className="text-[1.60rem] bg-[#1877F2] p-1.5 text-[#fff] rounded-lg"
              style={{ boxShadow: "0px 3px 10px 0px #1976D252" }}
            />
          </span>
        );
      case "instagram":
        return (
          <span>
            <FaInstagram
              className="text-[1.60rem] p-1.5 text-white rounded-lg"
              style={{
                background:
                  "linear-gradient(45deg, #feda75, #fa7e1e, #d62976, #962fbf, #4f5bd5)",
                boxShadow: "0px 4px 4px 0px rgba(0, 0, 0, 0.2)",
              }}
            />
          </span>
        );
      case "twitter":
        return (
          <span>
            <FaXTwitter
              className="text-[1.60rem] bg-[#000] p-1.5 text-[#fff] rounded-lg"
              style={{ boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.2)" }}
            />
          </span>
        );
      case "youtube":
        return (
          <span>
            <RiYoutubeFill
              className="text-[1.60rem] bg-[#E21A20] p-1.5 text-[#fff] rounded-lg"
              style={{ boxShadow: "0px 4px 4px 0px #E21A204F" }}
            />
          </span>
        );
      case "linkedin":
        return (
          <span>
            <img src={linkedinIcon} alt="LinkedIn" className="w-6 h-6" />
          </span>
        );
      case "all":
        return (
          <span>
            <FaGlobe
              className="text-[1.60rem] bg-gradient-to-r from-purple-500 to-blue-500 p-1.5 text-white rounded-lg"
              style={{ boxShadow: "0px 3px 10px 0px rgba(0, 0, 0, 0.2)" }}
            />
          </span>
        );
      default:
        return null;
    }
  };

  const getAlertDisplayColor = (alertDisplay: number | null) => {
    switch (alertDisplay) {
      case 1:
        return "bg-red-100 text-red-600";     // Hot
      case 2:
        return "bg-yellow-100 text-yellow-700"; // Warm
      case 3:
        return "bg-green-100 text-green-700";  // Cold
      case null:
        return "bg-gray-100 text-gray-500";    // No Value
      default:
        return "bg-blue-100 text-blue-600";    // Default (like 'Pending')
    }
  };

  return (
    <div className="bg-white rounded-3xl shadow xs:max-sm:mt-4 pt-4">
      <div className={`flex flex-col xs:max-sm:px-4 sm:px-4`}>
        <div className="w-full mb-4">
          <div className="relative w-full">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <IoIosSearch className="text-gray-400" />
            </div>
            <input
              type="text"
              placeholder={t("alerts-tablet.Search Posts") || "Search alerts..."}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-3xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              value={searchTerm}
              onChange={handleSearch}
            />
          </div>
        </div>

        <div className="flex flex-col sm:flex-row sm:flex-wrap gap-4 w-full">
          <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px]">
            <label htmlFor="date-filter" className="text-[#5A5A5A] mb-2 block">
              {t("alerts-tablet.date_filter")}
            </label>
            <Dropdown
              value={dateFilter}
              onChange={handleDateFilterChange}
              options={dateFilterOptions}
              placeholder="Select time frame"
              className="rounded-3xl h-[47px] w-full"
            />
          </div>

          {dateFilter === "custom" && (
            <>
              <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px]">
                <label htmlFor="start-date" className="text-[#5A5A5A] mb-2 block">
                  {t("alerts-tablet.begin")}
                </label>
                <input
                  id="start-date"
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="w-full rounded-lg border border-gray-200 bg-white px-3 h-[47px] py-2 text-gray-900"
                />
              </div>

              <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px]">
                <label htmlFor="end-date" className="text-[#5A5A5A] mb-2 block">
                  {t("alerts-tablet.end")}
                </label>
                <input
                  id="end-date"
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="w-full rounded-lg border border-gray-200 bg-white px-3 h-[47px] py-2 text-gray-900"
                />
              </div>
            </>
          )}

          <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px]">
            <label htmlFor="type" className="text-[#5A5A5A] mb-2 block">
              {t("alerts-tablet.type")}
            </label>
            <Dropdown
              value={postType}
              onChange={setPostType}
              options={options}
              placeholder="Select metric type"
              className="rounded-3xl h-[47px] w-full"
            />
          </div>

          <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px]">
            <label htmlFor="scope-filter" className="text-[#5A5A5A] mb-2 block">
              {t("alerts-tablet.scope")}
            </label>
            <Dropdown
              value={scopeFilter}
              onChange={setScopeFilter}
              options={scopeOptions}
              placeholder="Select scope"
              className="rounded-3xl h-[47px] w-full"
            />
          </div>

          <div className="w-full sm:flex-1 flex flex-col sm:flex-row items-end gap-4 mt-2 sm:mt-0">
            <div className="w-full sm:flex-1">
              <SocialNetworkChips
                setSocialNetwork={setSocialNetwork}
                socialNetwork={socialNetwork}
                t={t}
              />
            </div>
            <div className="w-full sm:w-auto flex flex-col sm:flex-row gap-4">
              <button
                onClick={handleFilter}
                className="bg-secondary w-full sm:w-auto flex items-center justify-center gap-2 btn text-[#ffffff] py-2 px-10 text-lg h-[47px] font-light rounded-xl"
              >
                <MdFilterList />
                {t("alerts-tablet.button")}
              </button>

              <button
                onClick={() => navigate("/alerts/sentiment")}
                className="bg-secondary w-full sm:w-auto flex items-center justify-center gap-2 btn text-[#ffffff] py-2 px-10 text-lg h-[47px] font-light rounded-xl"
              >
                <MdFilterList />
                {t("Add Sentiments")}
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-6 rounded-xl xs:max-sm:hidden">
        <div className="bg-white overflow-hidden">
          <div className="overflow-x-auto relative">
            <table className="min-w-full border-collapse text-left text-sm">
              <thead>
                <tr className="border-b border-gray-200 font-bold tracking-tight bg-[#EDF1F575] text-sm">
                  <th className="left-col">{t("alerts-tablet.thumbnail")}</th>
                  <th className="p-4" onClick={() => sortData("content")}>
                    <div className="flex items-center">
                      {t("alerts-tablet.content")}
                      <span className="ml-1 text-xs leading-none pl-1">
                        {sortField === "content" ? (sortDirection === "asc" ? "↑" : "↓") : "↑↓"}
                      </span>
                    </div>
                  </th>
                  <th className="p-4">{t("alerts-tablet.channel")}</th>
                  <th className="p-4" onClick={() => sortData("endDate")}>
                    <div className="flex items-center">
                      {t("alerts-tablet.endDate")}
                      <span className="ml-1 text-xs leading-none pl-1">
                        {sortField === "endDate" ? (sortDirection === "asc" ? "↑" : "↓") : "↑↓"}
                      </span>
                    </div>
                  </th>
                  <th className="p-4">{t("alerts-tablet.metricType")}</th>
                  <th className="p-4" onClick={() => sortData("scope")}>
                    <div className="flex items-center">
                      {t("alerts-tablet.scope")}
                      <span className="ml-1 text-xs leading-none pl-1">
                        {sortField === "scope" ? (sortDirection === "asc" ? "↑" : "↓") : "↑↓"}
                      </span>
                    </div>
                  </th>
                  <th className="p-4">{t("alerts-tablet.alert_count")}</th>
                  <th className="p-4">{t("alerts-tablet.metrics")}</th>
                  <th className="p-4" onClick={() => sortData("status")}>
                    <div className="flex items-center">
                      {t("alerts-tablet.status")}
                      <span className="ml-1 text-xs leading-none pl-1">
                        {sortField === "status" ? (sortDirection === "asc" ? "↑" : "↓") : "↑↓"}
                      </span>
                    </div>
                  </th>
                  <th className="right-col">{t("alerts-tablet.actions")}</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan={10}>
                      <Loader />
                    </td>
                  </tr>
                ) : paginatedData.length > 0 ? (
                  paginatedData.map((item: any) => (
                    <tr key={item.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="left-col relative">
                        {item["Image URL"] && item["Image URL"] !== "NA" ? (
                          <img
                            src={item["Image URL"]}
                            alt="Thumbnail"
                            className="w-10 h-10 rounded-xl"
                          />
                        ) : (
                          <img
                            src={thumbnail}
                            alt="Default Thumbnail"
                            className="w-10 h-10 rounded-xl"
                          />
                        )}
                      </td>

                      <td
                        className={`p-4 text-[0.85rem] text-nowrap truncate max-w-[150px] lg:min-w-[280px] ${getAlertDisplayColor(item.AlertDisplay)}`}
                        title={item.Content || "N/A"}
                      >
                        {item.Content || "N/A"}
                      </td>
                      <td className="p-4 min-w-[100px]">
                        <div className="flex items-center space-x-0.5 flex-wrap">
                          {getSocialNetworkIcon(item.Channel)}
                        </div>
                      </td>
                      <td className="p-4 text-[0.85rem]">
                        {new Date(item.EndDate).toLocaleDateString()}
                      </td>
                      <td className="p-4 text-[0.85rem]">
                        {item.MetricType || "N/A"}
                      </td>
                      <td className="p-4 text-[0.85rem]">
                        {item.Scope || "N/A"}
                      </td>
                      <td className="p-4 text-[0.85rem]">
                        {item["Alert Count"] ?? "N/A"}
                      </td>
                      <td className="p-4 text-[0.85rem]">
                        {item.Metrics || "N/A"}
                      </td>
                      <td className="p-4">
                        <button
                          disabled={!item.status}
                          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none ${item.status ? "bg-primary cursor-pointer" : "bg-gray-300 cursor-not-allowed"
                            }`}
                          onClick={() => handleToggleStatus(item)}
                        >
                          <span
                            className={`inline-block h-4 w-4 transform bg-white rounded-full transition-transform ${item.status ? "translate-x-6" : "translate-x-1"
                              }`}
                          />
                        </button>
                      </td>
                      <td className="right-col">
                        <div className="flex items-center gap-1">
                          <button
                            className="px-2 py-2 rounded-lg bg-[#21CE9E1A] cursor-pointer text-sm text-[#21CE9E]"
                            onClick={() => handleEditClick(item)}
                          >
                            <GoPencil size={16} />
                          </button>
                          <button
                            className="px-2 py-2 rounded-lg bg-[#21CE9E1A] cursor-pointer text-sm text-[#21CE9E]"
                            onClick={() => handleEditClick(item)}
                          >
                            <FaEye />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={10} className="text-center py-4">
                      No data available
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
            {!loading && (
              <div className="mt-4 px-8">
                <Pagination
                  currentPage={currentPage}
                  totalItems={data.length}
                  itemsPerPage={itemsPerPage}
                  onPageChange={handlePageChange}
                />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* MOBILE UI */}
      <div className="mt-6 rounded-xl hidden xs:max-sm:block">
        <div className="bg-white overflow-hidden">
          <div className="overflow-x-auto relative">
            <table className="min-w-full border-collapse text-left text-sm">
              <thead>
                <tr className="border-b border-gray-200 font-bold tracking-tight bg-[#EDF1F575] text-sm">
                  <th className="px-8 py-5">{t("alerts-tablet.thumbnail")}</th>
                </tr>
              </thead>
              <tbody className="flex flex-col">
                {loading ? (
                  <tr>
                    <td colSpan={7}>
                      <Loader />
                    </td>
                  </tr>
                ) : paginatedData.length > 0 ? (
                  paginatedData.map((item: any) => (
                    <tr key={item.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-4 px-8 flex flex-col gap-2 w-full">
                        <div className="flex justify-between items-center">
                          <div className="relative">
                            {item["Image URL"] !== "NA" ? (
                              <img
                                src={item["Image URL"] || thumbnail}
                                alt="Thumbnail"
                                className="w-15 h-15 rounded-xl"
                              />
                            ) : (
                              <img
                                src={thumbnail}
                                alt="Default Thumbnail"
                                className="w-15 h-15 rounded-xl"
                              />
                            )}
                            {item.AlertDisplay && (
                              <span
                                className={`absolute top-1 right-1 w-2 h-2 rounded-full ${getAlertDisplayColor(item.AlertDisplay)}`}
                              ></span>
                            )}
                          </div>
                          <div className="flex gap-2">
                            <button
                              className="px-2 py-2 rounded-lg mt-3.5 text-md bg-[#21CE9E1A] cursor-pointer text-xl text-[#21CE9E]"
                              onClick={() => handleEditClick(item)}
                            >
                              <GoPencil />
                            </button>
                          </div>
                        </div>
                        <div className="flex justify-between items-center mt-2">
                          <div className="flex items-center space-x-2">
                            {getSocialNetworkIcon(item.Channel)}
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="py-4 px-8 text-sm">
                              {new Date(item.EndDate).toLocaleDateString()}
                            </span>
                            <button
                              disabled={!item.status}
                              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none ${item.status ? "bg-primary cursor-pointer" : "bg-gray-300 cursor-not-allowed"
                                }`}
                              onClick={() => handleToggleStatus(item)}
                            >
                              <span
                                className={`inline-block h-4 w-4 transform bg-white rounded-full transition-transform ${item.status ? "translate-x-6" : "translate-x-1"
                                  }`}
                              />
                            </button>
                          </div>
                        </div>
                        <div className={`py-4 px-8 max-w-[200px] text-sm text-nowrap truncate ${getAlertDisplayColor(item.AlertDisplay)}`}>
                          {item.Content}
                        </div>
                        <div className="py-4 px-8 text-sm">
                          <strong>{t("alerts-tablet.scope")}:</strong> {item.Scope || "N/A"}
                        </div>
                        <div className="py-4 px-8 text-sm">
                          <strong>{t("alerts-tablet.alert_count")}:</strong> {item["Alert Count"] ?? "N/A"}
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={7} className="text-center py-4">
                      No data available
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
            {!loading && (
              <div className="mt-4 px-8">
                <Pagination
                  currentPage={currentPage}
                  totalItems={data.length}
                  itemsPerPage={itemsPerPage}
                  onPageChange={handlePageChange}
                />
              </div>
            )}
          </div>
        </div>
      </div>

      {isModalOpen && (
        <Editmodal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onOpenAddModal={() => {
            setIsModalOpen(false);
            setIsAddModalOpen(true);
          }}
          selectedPost={selectedPost}
          refreshData={() => fetchFilteredData()}
        />
      )}
      {isAddModalOpen && (
        <AddModal
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
          selectedPost={selectedPost}
          data={data}
          refreshData={() => fetchFilteredData()}
        />
      )}
    </div>
  );
}

export default Alerts;