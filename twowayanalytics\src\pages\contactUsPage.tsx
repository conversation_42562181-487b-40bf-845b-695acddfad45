import { useState } from 'react';
import { Mail, Send } from 'lucide-react';
import emailjs from '@emailjs/browser';

const ContactUsPage = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState('');

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus('');

    try {
      const serviceId = 'service_fybzs4k';
      const templateId = 'template_it7g02p';
      const publicKey = 'SMytzlAoPKjqtDtu5';

      const templateParams = {
        from_name: formData.name,
        from_email: formData.email,
        subject: formData.subject,
        message: formData.message
      };

      await emailjs.send(serviceId, templateId, templateParams, publicKey);
      setSubmitStatus('success');
      setFormData({ name: '', email: '', subject: '', message: '' });
    } catch (error) {
      console.error('Email send error:', error);
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen py-12 px-4 bg-[rgb(16,53,100)] text-white">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-[rgb(1,184,239)] mb-4">
            Get In Touch
          </h1>
          <p className="text-xl text-white max-w-2xl mx-auto">
            Ready to transform your data into actionable insights? Let's start a conversation.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-start">
          <div className="bg-white text-[rgb(16,53,100)] rounded-3xl p-8 shadow-2xl">
            <h2 className="text-3xl font-bold mb-4">Let's Connect</h2>
            <p className="mb-8">
              Have questions about our analytics solutions? Need a custom data strategy?
              We're here to help you unlock the power of your data.
            </p>

            <div className="space-y-6 mb-8">
              <div className="flex items-center space-x-4 p-4 bg-[rgb(16,53,100)] rounded-xl text-white">
                <Mail className="w-6 h-6" />
                <div>
                  <h3 className="font-semibold">Email</h3>
                  <p><EMAIL></p>
                </div>
              </div>
            </div>
            <div className="space-y-6 mb-8">
              <div className="flex items-center space-x-4 p-4 bg-[rgb(16,53,100)] rounded-xl text-white">
                <Mail className="w-6 h-6" />
                <div>
                  <h3 className="font-semibold">Email</h3>
                  <p><EMAIL></p>
                </div>
              </div>
            </div>
            <div className="space-y-6 mb-8">
              <div className="flex items-center space-x-4 p-4 bg-[rgb(16,53,100)] rounded-xl text-white">
                <Mail className="w-6 h-6" />
                <div>
                  <h3 className="font-semibold">Email</h3>
                  <p><EMAIL></p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white text-[rgb(16,53,100)] rounded-3xl p-6 sm:p-8 shadow-2xl max-w-3xl mx-auto">
            <h2 className="text-2xl sm:text-3xl font-bold mb-2 sm:mb-4">Send us a Message</h2>
            <p className="mb-6 text-gray-600 text-sm sm:text-base">
              Fill out the form below and we'll get back to you within 24 hours.
            </p>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Your Name *</label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:border-cyan-500 focus:ring-2 focus:ring-cyan-200 outline-none"
                    placeholder="John Doe"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Email Address *</label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:border-cyan-500 focus:ring-2 focus:ring-cyan-200 outline-none"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Subject *</label>
                <input
                  type="text"
                  name="subject"
                  value={formData.subject}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:border-cyan-500 focus:ring-2 focus:ring-cyan-200 outline-none"
                  placeholder="How can we help you?"
                  required
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Message *</label>
                <textarea
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  rows={5}
                  className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:border-cyan-500 focus:ring-2 focus:ring-cyan-200 outline-none resize-none"
                  placeholder="Tell us about your project or ask any questions..."
                  required
                />
              </div>

              {submitStatus === 'success' && (
                <div className="p-4 bg-green-100 text-green-800 rounded-xl">
                  ✅ Message sent successfully!
                </div>
              )}
              {submitStatus === 'error' && (
                <div className="p-4 bg-red-100 text-red-800 rounded-xl">
                  ❌ Something went wrong. Please try again.
                </div>
              )}

              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-[rgb(1,184,239)] text-white font-semibold py-4 px-8 rounded-xl hover:opacity-90 disabled:opacity-50 flex items-center justify-center space-x-3 cursor-pointer transition-colors"
              >
                {isSubmitting ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Sending...</span>
                  </>
                ) : (
                  <>
                    <Send className="w-5 h-5" />
                    <span>Send Message</span>
                  </>
                )}
              </button>
            </form>

            <div className="mt-6 text-center text-sm text-gray-500">
              By submitting this form, you agree to our privacy policy and terms of service.
            </div>
          </div>
        </div>

        <div className="mt-16 text-center bg-[rgb(1,184,239)] rounded-3xl p-8 text-white">
          <h3 className="text-2xl font-bold mb-4">Ready to Get Started?</h3>
          <p className="mb-6 max-w-2xl mx-auto">
            Join hundreds of businesses that trust 2WayAnalytics for their data insights and analytics solutions.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <button
              onClick={() => {
                const form = document.querySelector('form');
                if (form) {
                  form.scrollIntoView({ behavior: 'smooth' });
                }
              }}
              className="inline-flex items-center space-x-2 border-2 border-white text-white font-semibold px-6 py-3 rounded-xl hover:bg-white hover:text-[rgb(1,184,239)]"
            >
              <Mail className="w-5 h-5" />
              <span>Send Email</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactUsPage;
