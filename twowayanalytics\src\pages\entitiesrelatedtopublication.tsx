import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import Modal from "../components/Modal";
import apiService from "../services/apiService";

interface Association {
  AttributeId: string;
  Key: string;
  Value: string;
}

interface ApiResponse {
  associations: Association[];
}

function EntitiesRelatedtoPublication() {
  const { id } = useParams();
  const navigate = useNavigate();

  const [isOpen, setIsOpen] = useState(true);
  const [loading, setLoading] = useState<boolean>(false);
  const [data, setData] = useState<ApiResponse | null>(null);

  const fetchAllData = async () => {
    if (!id) return;

    setLoading(true);
    try {
      const response = await apiService.get<ApiResponse>(
        `/attribute-associations/publication-attribute-association?publicationId=${id}`
      );
      setData(response);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAllData();
  }, [id]);

  return (
    <Modal
      isOpen={isOpen}
      onClose={() => {
        setIsOpen(false);
        navigate("/publications");
      }}
      label="Entities Related to Publication"
    >
      {loading ? (
        <p className="text-center p-4">Loading...</p>
      ) : data && data.associations.length > 0 ? (
        <div className="overflow-x-auto mt-2">
          <table className="min-w-full border-collapse text-left">
            <thead>
              <tr className="border-b border-gray-200 text-[#9C9C9C] font-normal text-sm">
                <th className="p-3">Key</th>
                <th className="p-3 text-nowrap">Value</th>
              </tr>
            </thead>
            <tbody>
              {data.associations.map((item) => (
                <tr
                  key={item.AttributeId}
                  className="border-b border-gray-100 hover:bg-gray-50"
                >
                  <td className="p-3 font-medium text-nowrap">{item.Key}</td>
                  <td className="p-3 font-medium text-nowrap">{item.Value}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <p className="text-center p-4">No data available.</p>
      )}
    </Modal>
  );
}

export default EntitiesRelatedtoPublication;
