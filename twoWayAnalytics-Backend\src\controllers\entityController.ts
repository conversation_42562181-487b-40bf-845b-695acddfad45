import { Request, Response } from "express";
import { sql, poolPromise } from "../config/db";
import dotenv from "dotenv";
import fs from "fs";
import path from "path";
dotenv.config();

/**
 * @swagger
 * tags:
 *   name: Entities
 *   description: API for managing entities
 */

/**
 * Helper function to check if an entity exists in the database
 */
const checkIfExists = async (entityId: number): Promise<boolean> => {
    const pool = await poolPromise;
    const query = `SELECT COUNT(*) as count FROM ${process.env.CLIENT_NAME}.Entities WHERE EntityId = @entityId AND IsDeleted = 0`;

    const result = await pool.request()
        .input("entityId", sql.Int, entityId)
        .query(query);

    return result.recordset[0].count > 0;
};

/**
 * @swagger
 * /api/entities:
 *   get:
 *     summary: Get all non-deleted entities
 *     tags: [Entities]
 *     responses:
 *       200:
 *         description: List of entities
 */
export const getEntities = async (req: Request, res: Response) => {
    try {
        const pool = await poolPromise;
        const query = `SELECT EntityId, Entity FROM ${process.env.CLIENT_NAME}.Entities WHERE IsDeleted = 0`;
        const result = await pool.request().query(query);
        res.json(result.recordset);
    } catch (err) {
        console.error("Error fetching entities:", err);
        res.status(500).json({ error: "Server error" });
    }
};

/**
 * @swagger
 * /api/entities:
 *   post:
 *     summary: Create a new entity
 *     tags: [Entities]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               entity:
 *                 type: string
 *                 example: "Player"
 *     responses:
 *       201:
 *         description: Entity created successfully
 */
export const createEntity = async (req: Request, res: Response): Promise<void> => {
    const { entity } = req.body;

    if (!entity) {
        res.status(400).json({ error: "Entity name is required" });
        return;
    }

    try {
        const pool = await poolPromise;
        const query = `INSERT INTO ${process.env.CLIENT_NAME}.Entities (Entity) OUTPUT INSERTED.EntityId VALUES (@entity)`;
        const result = await pool.request()
            .input("entity", sql.NVarChar, entity)
            .query(query);

        res.status(201).json({
            message: "Entity created successfully",
            entityId: result.recordset[0].EntityId,
        });
    } catch (err) {
        console.error("Error creating entity:", err);
        res.status(500).json({ error: "Server error" });
    }
};

/**
 * @swagger
 * /api/entities/{id}:
 *   delete:
 *     summary: Soft delete an entity
 *     tags: [Entities]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Entity deleted successfully
 */
export const deleteEntity = async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;

    if (!id || isNaN(Number(id))) {
        res.status(400).json({ error: "Invalid Entity ID" });
        return;
    }

    try {
        const exists = await checkIfExists(Number(id));
        if (!exists) {
            res.status(404).json({ message: "Entity not found" });
            return;
        }

        const pool = await poolPromise;
        const query = `UPDATE ${process.env.CLIENT_NAME}.Entities SET IsDeleted = 1 WHERE EntityId = @id`;
        const result = await pool.request()
            .input("id", sql.Int, id)
            .query(query);

        if (result.rowsAffected[0] === 0) {
            res.status(404).json({ message: "Entity not found" });
            return;
        }

        res.json({ message: "Entity deleted successfully" });
    } catch (err) {
        console.error("Error deleting entity:", err);
        res.status(500).json({ error: "Server error" });
    }
};

/**
 * @swagger
 * /api/entities/{id}:
 *   put:
 *     summary: Update an entity
 *     tags: [Entities]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *       - in: body
 *         name: entity
 *         required: true
 *         schema:
 *           type: object
 *           properties:
 *             entity:
 *               type: string
 *     responses:
 *       200:
 *         description: Entity updated successfully
 *       404:
 *         description: Entity not found
 */
export const updateEntity = async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;
    const { entity } = req.body;

    if (!id || isNaN(Number(id))) {
        res.status(400).json({ error: "Invalid Entity ID" });
        return;
    }

    if (!entity) {
        res.status(400).json({ error: "Entity name is required" });
        return;
    }

    try {
        const exists = await checkIfExists(Number(id));
        if (!exists) {
            res.status(404).json({ message: "Entity not found" });
            return;
        }
        const pool = await poolPromise;
        const query = `UPDATE ${process.env.CLIENT_NAME}.Entities SET Entity = @entity WHERE EntityId = @id AND IsDeleted = 0`;
        const result = await pool.request()
            .input("id", sql.Int, id)
            .input("entity", sql.NVarChar, entity)
            .query(query);

        if (result.rowsAffected[0] === 0) {
            res.status(404).json({ message: "Entity not found or already deleted" });
            return;
        }

        res.json({ message: "Entity updated successfully" });
    } catch (err) {
        console.error("Error updating entity:", err);
        res.status(500).json({ error: "Server error" });
    }
};

/**
 * @swagger
 * /api/entities/{id}/image:
 *   put:
 *     summary: Update an entity's image
 *     tags: [Entities]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               image:
 *                 type: string
 *                 format: binary
 *     responses:
 *       200:
 *         description: Image updated successfully
 *       400:
 *         description: No image provided
 *       404:
 *         description: Entity not found
 *       500:
 *         description: Server error
 */
export const updateEntityImage = async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;

    if (!id || isNaN(Number(id))) {
        res.status(400).json({ error: "Invalid Entity ID" });
        return;
    }

    try {

        const exists = await checkIfExists(Number(id));
        if (!exists) {
            res.status(404).json({ message: "Entity not found" });
            return;
        }
        const pool = await poolPromise;
        const query = `SELECT EntityId FROM ${process.env.CLIENT_NAME}.Entities WHERE EntityId = @id AND IsDeleted = 0`;
        const checkEntity = await pool.request()
            .input("id", sql.Int, id)
            .query(query);

        if (checkEntity.recordset.length === 0) {
            res.status(404).json({ error: "Entity not found" });
            return;
        }

        if (!req.file) {
            res.status(400).json({ error: "No image uploaded" });
            return;
        }

        const imagePath = path.join("DataLake", "media", process.env.CLIENT_NAME || "default", "Entities", `${id}.jpg`);

        if (fs.existsSync(imagePath)) {
            fs.unlinkSync(imagePath);
        }

        fs.writeFileSync(imagePath, req.file.buffer);

        res.json({ message: "Image updated successfully", path: imagePath });
    } catch (err) {
        console.error("Error updating entity image:", err);
        res.status(500).json({ error: "Server error" });
    }
};