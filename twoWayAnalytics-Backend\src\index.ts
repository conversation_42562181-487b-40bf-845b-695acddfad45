import express from "express";
import cors from "cors";
import dotenv from "dotenv";
import entityRoutes from "./routes/entityRoutes";
import swaggerJsdoc from "swagger-jsdoc";
import swaggerUi from "swagger-ui-express";
dotenv.config();
import { ConfidentialClientApplication, AuthenticationResult } from "@azure/msal-node";
import alertsRoutes from "./routes/alertsRoutes";

const CLIENT_ID = '69903f42-622d-49c3-a4e9-4c55349cb8f5'; 
const TENANT_ID = 'ccdd6c63-ac71-4ab9-ae96-54b0443f664e';
const CLIENT_SECRET = '****************************************';
const POWER_BI_SCOPES = ["https://analysis.windows.net/powerbi/api/.default"];
// declare const cca: ConfidentialClientApplication;

const cca = new ConfidentialClientApplication({
    auth: {
      clientId: CLIENT_ID || "<your-client-id>",
      authority: `https://login.microsoftonline.com/${TENANT_ID}`,
      clientSecret: CLIENT_SECRET || "<your-client-secret>",
    },
  });

const app = express();
app.use(cors()); // Allow all origins

// OR configure CORS explicitly
app.use(cors({
    origin: "*", 
    methods: "GET,HEAD,PUT,PATCH,POST,DELETE",
    allowedHeaders: "Content-Type,Authorization",
}));
app.use(express.json());

app.get("/api/test", (req, res) => {
    res.json({ message: "API is working!" });
});

// Backend endpoint to get Power BI Embedded token
app.post("/api/get-powerbi-token", async (req: any, res: any) => {
    const microsoftToken = req.headers.authorization?.split(" ")[1];
  
    if (!microsoftToken) {
      return res.status(401).json({ error: "Microsoft token not provided." });
    }
  
    try {
      const powerBITokenResponse = await acquirePowerBIToken(microsoftToken);
      if (powerBITokenResponse?.accessToken) {
        res.json({ powerBIToken: powerBITokenResponse.accessToken });
      } else {
        console.error("Failed to acquire Power BI token: No access token received.");
        res.status(500).json({ error: "Failed to acquire Power BI token." });
      }
    } catch (error) {
      console.error("Error acquiring Power BI token:", error);
      res.status(500).json({ error: "Failed to acquire Power BI token." });
    }
  });
  
  // Function to acquire Power BI Embedded token on behalf of a user
async function acquirePowerBIToken(microsoftToken: string): Promise<AuthenticationResult | null> {
    try {
      if (!CLIENT_ID || !TENANT_ID || !CLIENT_SECRET) {
        console.error("Azure AD configuration or ConfidentialClientApplication instance is missing.");
        return null;
      }
  
      const silentAuthResult = await cca.acquireTokenOnBehalfOf({
        scopes: POWER_BI_SCOPES,
        oboAssertion: microsoftToken,
      });
  
      return silentAuthResult;
    } catch (error: any) {
      console.error("Error acquiring Power BI token on behalf of user:", error);
      return null;
    }
  }
  


// Swagger Setup
const swaggerOptions = {
    definition: {
        openapi: "3.0.0",
        info: {
            title: "2 Way Analytics API",
            version: "1.0.0",
            description: "API for managing entities and attributes with associations",
        },
        servers: [{ url: "http://localhost:5000", description: "Local development server" }],
    },
    apis:  [__filename, "./src/routes/*.ts", "./src/controllers/*.ts"],
};

const swaggerDocs = swaggerJsdoc(swaggerOptions);
app.use("/api-docs", swaggerUi.serve, swaggerUi.setup(swaggerDocs));


app.use("/api/V1", entityRoutes, alertsRoutes);

const PORT = process.env.PORT || 5000;
app.listen(PORT, () => console.log(`🚀 Server running on port ${PORT}`));
