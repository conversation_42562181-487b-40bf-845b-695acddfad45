import { useTranslation } from "react-i18next";
import { useState, useEffect } from "react";
import { BsArrowRight, BsTwitterX } from "react-icons/bs";
import { FaFacebookF, FaInstagram, FaYoutube } from "react-icons/fa";
import api from "../services/apiService";
import { FaMeta } from "react-icons/fa6";

type Platform = "youtube" | "twitter" | "facebook";

interface LoginStatus {
  youtube: boolean;
  twitter: boolean;
  facebook: boolean;
}

function Socialmedia() {
  const { t } = useTranslation();
  const [loginStatus, setLoginStatus] = useState<LoginStatus>({
    youtube: false,
    twitter: false,
    facebook: false
  });
  const [loading, setLoading] = useState(true);
  const [loggingOut, setLoggingOut] = useState<Platform | null>(null);

  const userData = JSON.parse(localStorage.getItem("UserData") || "{}");
  const pkg = userData.pkg?.toLowerCase() || "unknown";
  const isTwitterDisabled = pkg === "unknown" || pkg === "free" || pkg === "basic";

  const fetchLoginStatus = async () => {
    try {
      const response = await api.get("/linking/status");

      if (response && typeof response === 'object') {
        const status = response as Partial<LoginStatus>;
        setLoginStatus({
          youtube: Boolean(status.youtube),
          twitter: Boolean(status.twitter),
          facebook: Boolean(status.facebook)
        });
      } else {
        console.error("Invalid response format:", response);
      }
    } catch (error) {
      console.error("Error fetching login status:", error);
    }
  };

  useEffect(() => {
    const initializeStatus = async () => {
      setLoading(true);
      await fetchLoginStatus();
      setLoading(false);
    };

    initializeStatus();
  }, []);

  const handleLogin = (platform: Platform) => {
    if (platform === "twitter" && isTwitterDisabled) {
      return;
    }
    api.sociallogin(platform);
  };

  const handleLogout = async (platform: Platform) => {
    if (loggingOut === platform) {
      return;
    }

    setLoggingOut(platform);

    try {
      const response = await api.get(`/linking/${platform}/logout`);

      if (response) {
        await fetchLoginStatus();
        console.log(`Successfully logged out from ${platform}`);
      } else {
        const status = response && typeof response === "object" && "status" in response ? (response as any).status : "unknown";
        const statusText = response && typeof response === "object" && "statusText" in response ? (response as any).statusText : "";
        console.error(`Error logging out from ${platform}:`, status, statusText);
      }
    } catch (error: any) {
      console.error(`Error logging out from ${platform}:`, error.response?.data || error.message);
    } finally {
      setLoggingOut(null);
    }
  };


  // Loading state
  if (loading) {
    return (
      <div className="my-4">
        <h1 className="pl-2 font-bold text-xl">{t("socialmedia.socialMediaLinkingTitle")}</h1>
        <div className="flex gap-6 mt-6 xs:max-sm:flex-col">
          {[1, 2, 3].map((index) => (
            <div key={index} className="p-6 max-w-[350px] rounded-3xl shadow bg-white">
              <div className="animate-pulse">
                <div className="h-12 bg-gray-200 rounded-2xl mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="my-4">
      <h1 className="pl-2 font-bold text-xl">{t("socialmedia.socialMediaLinkingTitle")}</h1>
      <div className="flex gap-6 mt-6 xs:max-sm:flex-col">

        {/* YouTube Login/Logout */}
        <div className="p-6 flex-col xs:max-sm:max-w-[400px] xs:max-sm:mx-auto xs:max-sm:py-8 xs:max-sm:px-4 max-w-[350px] items-center rounded-3xl shadow flex bg-white">
          <button
            className={`flex items-center text-[1rem] justify-between rounded-2xl w-full py-2.5 px-3 cursor-pointer transition-colors duration-200 ${loginStatus.youtube
              ? "bg-[#********] text-[#C70000] hover:bg-[#********]"
              : "bg-[#F200000D] text-[#F20000] hover:bg-[#********]"
              }`}
            onClick={() => loginStatus.youtube ? handleLogout("youtube") : handleLogin("youtube")}
            disabled={loggingOut === "youtube"}
          >
            <span className="flex items-center gap-2">
              <span className={`p-2 rounded-full ${loginStatus.youtube ? "bg-[#C70000]" : "bg-[#F20000]"}`}>
                <FaYoutube size={24} className="text-white" />
              </span>
              {loggingOut === "youtube"
                ? t("socialmedia.loggingOut", "Logging out...")
                : loginStatus.youtube
                  ? t("socialmedia.youtubeLogoutButton")
                  : t("socialmedia.youtubeLoginButton")
              }
            </span>
            <BsArrowRight
              size={24}
              className={`${loginStatus.youtube ? "text-[#C70000]" : "text-[#F20000]"} ${loggingOut === "youtube" ? "animate-spin" : ""
                }`}
            />
          </button>
          <p className="mt-4 text-[#4B5563]">
            <strong className="text-black mr-2">{t("socialmedia.permissions")}</strong>
            {t("socialmedia.youtubePermissions")}
          </p>
        </div>

        {/* Twitter (X) Login/Logout */}
        <div className="p-6 flex-col xs:max-sm:max-w-[400px] xs:max-sm:mx-auto xs:max-sm:py-8 xs:max-sm:px-4 max-w-[350px] items-center rounded-3xl shadow flex bg-white">
          <button
            className={`flex items-center text-[1rem] justify-between rounded-2xl w-full py-2.5 px-3 transition-colors duration-200 ${isTwitterDisabled
              ? "bg-gray-300 text-gray-400 cursor-not-allowed"
              : loginStatus.twitter
                ? "bg-[#00000026] text-[#333333] cursor-pointer hover:bg-[#00000040]"
                : "bg-[#00000012] text-[#000000] cursor-pointer hover:bg-[#00000020]"
              }`}
            disabled={isTwitterDisabled || loggingOut === "twitter"}
            onClick={() => {
              if (!isTwitterDisabled) {
                loginStatus.twitter ? handleLogout("twitter") : handleLogin("twitter");
              }
            }}
            title={isTwitterDisabled ? "This feature is not available for your plan" : ""}
          >
            <span className="flex items-center gap-2">
              <span className={`p-2 rounded-full ${isTwitterDisabled
                ? "bg-gray-400"
                : loginStatus.twitter
                  ? "bg-[#333333]"
                  : "bg-[#000000]"
                }`}>
                <BsTwitterX size={24} className="text-white" />
              </span>
              {isTwitterDisabled
                ? t("socialmedia.xLoginButton")
                : loggingOut === "twitter"
                  ? t("socialmedia.loggingOut", "Logging out...")
                  : loginStatus.twitter
                    ? t("socialmedia.xLogoutButton")
                    : t("socialmedia.xLoginButton")
              }
            </span>
            <BsArrowRight
              size={24}
              className={`${isTwitterDisabled
                ? "text-gray-400"
                : loginStatus.twitter
                  ? "text-[#333333]"
                  : "text-[#000000]"
                } ${loggingOut === "twitter" ? "animate-spin" : ""}`}
            />
          </button>
          <p className="mt-4 text-[#4B5563]">
            <strong className="text-[#000000] mr-2">{t("socialmedia.permissions")}</strong>
            {t("socialmedia.xPermissions")}
          </p>
        </div>

        {/* Facebook Login/Logout */}
        <div className="p-6 flex-col xs:max-sm:max-w-[400px] xs:max-sm:mx-auto xs:max-sm:py-8 xs:max-sm:px-4 max-w-[350px] items-center rounded-3xl shadow flex bg-white">
          <button
            className={`flex items-center text-[1rem] justify-between rounded-2xl w-full py-2.5 px-3 cursor-pointer transition-colors duration-200 ${loginStatus.facebook
              ? "bg-[#3A559F36] text-[#2B4177] hover:bg-[#3A559F50]"
              : "bg-[#3A559F21] text-[#3A559F] hover:bg-[#3A559F30]"
              }`}
            onClick={() => loginStatus.facebook ? handleLogout("facebook") : handleLogin("facebook")}
            disabled={loggingOut === "facebook"}
          >
            <span className="flex items-center gap-2">
              <span className={`p-2 rounded-full ${loginStatus.facebook ? "bg-[#2B4177]" : "bg-[#3A559F]"}`}>
                <FaMeta size={24} className="text-white" />
              </span>
              {loggingOut === "facebook"
                ? t("socialmedia.loggingOut", "Logging out...")
                : loginStatus.facebook
                  ? t("socialmedia.facebookLogoutButton")
                  : t("socialmedia.facebookLoginButton")
              }
            </span>
            <BsArrowRight
              size={24}
              className={`${loginStatus.facebook ? "text-[#2B4177]" : "text-[#3A559F]"} ${loggingOut === "facebook" ? "animate-spin" : ""
                }`}
            />
          </button>
          <p className="mt-4 text-[#4B5563]">
            <strong className="text-black mr-2">{t("socialmedia.permissions")}</strong>
            {t("socialmedia.facebookPermissions")}
            <span className="flex items-center gap-2 ml-2">
              <span className="w-8 h-8 flex items-center justify-center rounded-full bg-[#1877F2]">
                <FaFacebookF size={18} className="text-white" />
              </span>
              <span
                className="w-8 h-8 flex items-center justify-center rounded-full text-white"
                style={{
                  background: "linear-gradient(45deg, #feda75, #fa7e1e, #d62976, #962fbf, #4f5bd5)"
                }}
              >
                <FaInstagram size={18} />
              </span>
            </span>


          </p>
        </div>
      </div>
    </div>
  );
}

export default Socialmedia;