// import { useEffect, useState } from "react";
// import { useTranslation } from "react-i18next";
// import { useParams } from "react-router-dom";
// import apiService from "../services/apiService";
// import Loader from "./loader";
// import { FaLink } from "react-icons/fa";
// import DataModal from "./datamodal";


// function LinkEntitiesToPost() {
//   const { t } = useTranslation();
//   const { id } = useParams<{ id: string }>();
//   const [loading, setLoading] = useState(false);
//   const [attributes, setAttributeData] = useState<any[]>([]);
//   const [isModalOpen, setIsModalOpen] = useState(false);
//   const [modalData, setModalData] = useState({ selectedId: null, selectedKey: "" });


//   const fetchData = async () => {
//     setLoading(true);
//     try {
//       const response = await apiService.get("attributes");
//       setAttributeData(response as any[]);
//     } catch (error) {
//       console.error("Error fetching data:", error);
//     } finally {
//       setLoading(false);
//     }
//   };

//   useEffect(() => {
//     fetchData();
//   }, [id]);

//   const groupedAttributes = attributes.reduce(
//     (acc: Record<string, any[]>, attr) => {
//       if (!acc[attr.Key]) acc[attr.Key] = [];
//       acc[attr.Key].push(attr);
//       return acc;
//     },
//     {}
//   );
//   return (
//     <div className="mt-4">
//       {loading ? (
//         <Loader />
//       ) : (
//         <>
//           <div className="flex justify-between items-center my-4">
//             <h1 className="pl-2 font-bold text-xl xs:max-sm:text-lg xs:max-sm:max-w-[120px]">
//               {t("linkEntitie.linkEntitiesToPostTitle")}
//             </h1>
//             <div className="flex xs:max-sm:flex-col items-center gap-3 xs:max-sm:gap-1 justify-start">
//             </div>
//           </div>

//           <div className="bg-[#F4F7FB] hidden sm:block py-4">
//             {Object.entries(groupedAttributes).map(([key, values]) => (
//               <div
//                 key={key}
//                 className="mb-6 rounded-xl bg-white"
//               >
//                 <div className="overflow-hidden rounded-xl">
//                   <div className="overflow-x-auto relative">
//                     <div className="max-h-[230px] overflow-y-auto">
//                       <table className="w-full table-auto">
//                         <thead className="min-w-full border-collapse text-left text-sm sticky top-0 bg-white z-[1]">
//                           <tr className="border-b border-gray-200 font-bold tracking-tight bg-[#F3FAFD] text-sm">
//                             <th scope="col" className="left-col w-1/2 text-ellipsis overflow-hidden whitespace-nowrap px-4 py-3">
//                               {key}
//                             </th>
//                             <th scope="col" className="p-4 text-ellipsis overflow-hidden whitespace-nowrap">
//                               {t("entity.numberOfLinks")}
//                             </th>
//                             <th scope="col" className="right-col text-ellipsis overflow-hidden whitespace-nowrap px-4 py-3">
//                               {t("table.columns.actions")}
//                             </th>
//                           </tr>
//                         </thead>
//                         <tbody>
//                           {values.map((item) => (
//                             <tr
//                               key={item.AttributeId}
//                               className="border-b border-gray-100 hover:bg-gray-50"
//                             >
//                               <td className="left-col w-1/2 text-ellipsis overflow-hidden whitespace-nowrap px-4 py-3">
//                                 {item.Value}
//                               </td>
//                               <td className="p-4 text-ellipsis overflow-hidden whitespace-nowrap">
//                                 {item.AssociationCount }
//                               </td>
//                               <td className="right-col px-4 py-3">
//                                 <button
//                                   className="px-2 py-2 rounded-lg bg-[#21CE9E1A] cursor-pointer text-sm text-[#21CE9E]"
//                                   onClick={() => {
//                                     setModalData({
//                                       selectedId: item.AttributeId,
//                                       selectedKey: item.Value,
//                                     });
//                                     setIsModalOpen(true);
//                                   }}
//                                 >
//                                   <FaLink />
//                                 </button>
//                               </td>
//                             </tr>
//                           ))}
//                         </tbody>
//                       </table>
//                     </div>
//                   </div>
//                 </div>
//               </div>
//             ))}
//             {Object.keys(groupedAttributes).length === 0 && (
//               <div className="text-center py-4 text-sm text-gray-600 mx-4">
//                 {t("noDataAvailable")}
//               </div>
//             )}
//           </div>

//           {/* MOBILE UI */}
//           <div className="block sm:hidden">
//             {Object.entries(groupedAttributes).map(([key, values]) => (
//               <div key={key} className="mb-6 rounded-xl bg-white shadow">
//                 <h2 className="text-sm font-bold text-gray-900 px-4 py-3 border-b border-gray-200 bg-[#EDF1F575]">
//                   {key}
//                 </h2>
//                 {values.map((item) => (
//                   <div
//                     key={item.AttributeId}
//                     className="border-b border-gray-100 last:border-b-0 px-4 py-3 hover:bg-gray-50"
//                   >
//                     <div className="flex justify-between items-center">
//                       <div className="text-sm text-gray-800">{item.Value}</div>
//                       <button
//                         className="px-2 py-1 rounded-lg bg-[#21CE9E1A] text-[#21CE9E]"
//                         onClick={() => {
//                           setModalData({
//                             selectedId: item.AttributeId,
//                             selectedKey: item.Value,
//                           });
//                           setIsModalOpen(true);
//                         }}                      >
//                         <FaLink size={16} />
//                       </button>
//                     </div>
//                     <div className="text-sm text-gray-600 mt-1">
//                       {t("entity.numberOfLinks")}: {item.EntityId ? item.EntityId : "0"}
//                     </div>
//                   </div>
//                 ))}
//               </div>
//             ))}
//             {Object.keys(groupedAttributes).length === 0 && (
//               <div className="text-center py-4 text-sm text-gray-600">
//                 {t("noDataAvailable")}
//               </div>
//             )}
//           </div>

//           <DataModal
//             isOpen={isModalOpen}
//             onClose={() => setIsModalOpen(false)}
//             attributeId={modalData.selectedId}
//             attributeKey={modalData.selectedKey}
//           />
//         </>
//       )}
//     </div>
//   );
// }

// export default LinkEntitiesToPost;
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";
import { FaLink, FaChevronDown, FaChevronRight } from "react-icons/fa";
import apiService from "../services/apiService";
import Loader from "./loader";
import DataModal from "./datamodal";

function LinkEntitiesToPost() {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const [loading, setLoading] = useState(false);
  const [attributes, setAttributeData] = useState<any[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalData, setModalData] = useState({ selectedId: null, selectedKey: "" });
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});

  const fetchData = async () => {
    setLoading(true);
    try {
      const response = await apiService.get("attributes");
      setAttributeData(response as any[]);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [id]);

  const groupedAttributes = attributes.reduce((acc: Record<string, any[]>, attr) => {
    if (!acc[attr.Key]) acc[attr.Key] = [];
    acc[attr.Key].push(attr);
    return acc;
  }, {});

  const toggleSection = (key: string) => {
    setExpandedSections((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  return (
    <div className="mt-6 px-4">
      {loading ? (
        <Loader />
      ) : (
        <>
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-semibold text-gray-800 ">
              {t("entity.linkEntitiesToPostTitle")}
            </h1>
          </div>

          {/* DESKTOP TABLE UI */}
          <div className="hidden sm:block">
            {Object.entries(groupedAttributes).map(([key, values]) => {
              const isExpanded = expandedSections[key];
              return (
                <div key={key} className="mb-6 rounded-xl bg-white shadow-sm border border-gray-200">
                  <div
                    className="flex justify-between items-center px-6 py-4 bg-[#F3FAFD] rounded-t-xl border-b border-gray-200 cursor-pointer"
                    onClick={() => toggleSection(key)}
                  >
                    <h2 className="font-semibold text-sm text-gray-800">{key}</h2>
                    {isExpanded ? <FaChevronDown /> : <FaChevronRight />}
                  </div>

                  {isExpanded && (
                    <div className="overflow-x-auto max-h-[260px] overflow-y-auto">
                      <table className="w-full text-sm text-left text-gray-700">
                        <thead className="sticky top-0 bg-white z-10">
                          <tr className="text-xs font-semibold border-b border-gray-200">
                            <th className="px-6 py-3">{t("entity.value")}</th>
                            <th className="px-6 py-3">{t("entity.numberOfLinks")}</th>
                            <th className="px-6 py-3 ">{t("entity.actions")}</th>
                          </tr>
                        </thead>
                        <tbody>
                          {values.map((item) => (
                            <tr key={item.AttributeId} className="hover:bg-gray-50">
                              <td className="px-6 py-3">{item.Value}</td>
                              <td className="px-6 py-3">{item.AssociationCount}</td>
                              <td className="px-6 py-3 text-right">
                                <button
                                  className="flex items-center justify-center gap-1 px-3 py-2 text-sm text-[#21CE9E] bg-[#21CE9E1A] hover:bg-[#21ce9e2e] rounded-md transition"
                                  onClick={() => {
                                    setModalData({ selectedId: item.AttributeId, selectedKey: item.Value });
                                    setIsModalOpen(true);
                                  }}
                                >
                                  <FaLink className="text-md" />
                                  {t("entity.link")}
                                </button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </div>
              );
            })}
            {Object.keys(groupedAttributes).length === 0 && (
              <div className="text-center py-6 text-gray-500">{t("entity.noDataAvailable")}</div>
            )}
          </div>

          {/* MOBILE UI */}
          <div className="sm:hidden">
            {Object.entries(groupedAttributes).map(([key, values]) => {
              const isExpanded = expandedSections[key];
              return (
                <div key={key} className="mb-4 rounded-xl bg-white shadow border border-gray-200">
                  <div
                    className="flex justify-between items-center bg-[#EDF1F5] px-4 py-2 rounded-t-xl cursor-pointer"
                    onClick={() => toggleSection(key)}
                  >
                    <h2 className="text-sm font-semibold text-gray-800">{key}</h2>
                    {isExpanded ? <FaChevronDown /> : <FaChevronRight />}
                  </div>

                  {isExpanded &&
                    values.map((item) => (
                      <div
                        key={item.AttributeId}
                        className="px-4 py-3 border-b last:border-b-0 border-gray-200 hover:bg-gray-50 transition"
                      >
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium text-gray-800">{item.Value}</span>
                          <button
                            className="p-2 text-[#21CE9E] bg-[#21CE9E1A] hover:bg-[#21ce9e2e] rounded-lg transition"
                            onClick={() => {
                              setModalData({ selectedId: item.AttributeId, selectedKey: item.Value });
                              setIsModalOpen(true);
                            }}
                          >
                            <FaLink size={16} />
                          </button>
                        </div>
                        <div className="text-sm text-gray-600 mt-1">
                          {t("entity.numberOfLinks")}: {item.AssociationCount || "0"}
                        </div>
                      </div>
                    ))}
                </div>
              );
            })}
            {Object.keys(groupedAttributes).length === 0 && (
              <div className="text-center py-6 text-gray-500">{t("entity.noDataAvailable")}</div>
            )}
          </div>

          {/* MODAL */}
          <DataModal
            isOpen={isModalOpen}
            onClose={() => setIsModalOpen(false)}
            attributeId={modalData.selectedId}
            attributeKey={modalData.selectedKey}
          />
        </>
      )}
    </div>
  );
}

export default LinkEntitiesToPost;