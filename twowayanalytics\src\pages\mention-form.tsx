import { Check, X } from "lucide-react";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Fa<PERSON><PERSON><PERSON><PERSON>, FaInst<PERSON>ram, FaXTwitter } from "react-icons/fa6";
import { IoIosSearch } from "react-icons/io";
import { MdFilterList } from "react-icons/md";
import { RiYoutubeFill } from "react-icons/ri";
import Switch from '@mui/material/Switch';
import FormControlLabel from '@mui/material/FormControlLabel';
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import Button from '@mui/material/Button';
import { styled } from '@mui/material/styles';
import Dropdown from "../components/Dropdown";
import Pagination from "../components/Pagination";
import apiService from "../services/apiService";
import linkedinIcon from "../assets/Linkedin.svg";
import thumbnail from "../assets/thumbnail.svg";
import Loader from "./loader";
import { useParams, useNavigate } from "react-router-dom";
import Chip from '@mui/material/Chip';
import TextField from '@mui/material/TextField';
import Box from '@mui/material/Box';

export interface AlertConfig {
  scope: 'all' | 'entity' | 'content';
  entityType?: string;
  entityAttribute?: string;
  contentChannel?: string;
  contentText?: string;
  wordCount: number;
  trackedWords: string[];
  duration?: string;
  repeatable: boolean;
  startDate?: Date;
  endDate?: Date;
  selectedPublicationId?: number;
  selectedPublicationText?: string;
}

interface DropdownOption {
  value: string;
  label: string;
}

interface Entity {
  type: string;
  attributes: string[];
}

interface Attribute {
  id: string;
  AttributeId: string;
  Key: string;
  Value: string;
  EntityId: string;
  Entity: string;
  thumbnail?: string;
}

const ScopeButton = styled(Button)(({ }) => ({
  textTransform: 'none',
  fontWeight: 'normal',
  flex: 1,
  minWidth: '100px',
  margin: '0 4px',
  transition: 'all 0.3s ease',
}));

const ActionButton = styled(Button)(({ theme }) => ({
  textTransform: 'none',
  backgroundColor: theme.palette.primary.light,
  color: theme.palette.getContrastText(theme.palette.primary.light),
  '&:hover': {
    backgroundColor: theme.palette.primary.main,
  },
}));

const AlertScopeConfig = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [entities, setEntities] = useState<Entity[]>([]);
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [originalData, setOriginalData] = useState<any[]>([]);
  const [itemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [dateFilter, setDateFilter] = useState("");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [postType, setPostType] = useState("");
  const [trackedWordInput, setTrackedWordInput] = useState("");

  const { id } = useParams<{ id: string }>();

  type SortField = 'text' | 'date' | 'status' | '';
  type SortDirection = 'asc' | 'desc';
  const [sortField, setSortField] = useState<SortField>('date');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');

  // Calculate pagination values
  const totalItems = data.length;
  const pageSize = itemsPerPage;
  const totalPages = Math.ceil(totalItems / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = Math.min(startIndex + pageSize, totalItems);
  const paginatedData = data.slice(startIndex, endIndex);

  const [config, setConfig] = useState<AlertConfig>({
    scope: 'all',
    wordCount: 10,
    trackedWords: [],
    repeatable: false,
  });

  const durationOptions: DropdownOption[] = [
    { value: '', label: t('alertConfig.noDuration') },
    { value: '1h', label: t('alertConfig.1h') },
    { value: '2h', label: t('alertConfig.2h') },
    { value: '4h', label: t('alertConfig.4h') },
    { value: '12h', label: t('alertConfig.12h') },
    { value: '1d', label: t('alertConfig.1d') },
    { value: '2d', label: t('alertConfig.2d') },
    { value: '7d', label: t('alertConfig.7d') },
    { value: '30d', label: t('alertConfig.30d') },
  ];

  const channelOptions: DropdownOption[] = [
    { value: 'all', label: t('alertConfig.allChannels') },
    { value: 'facebook', label: 'Facebook' },
    { value: 'twitter', label: 'X (Twitter)' },
    { value: 'instagram', label: 'Instagram' },
    { value: 'youtube', label: 'YouTube' },
  ];

  const entityTypeOptions: DropdownOption[] = entities.map(entity => ({
    value: entity.type.toLowerCase(),
    label: entity.type
  }));

  const attributeOptions: DropdownOption[] = config.entityType
    ? entities.find(e => e.type.toLowerCase() === config.entityType)?.attributes.map(attr => ({
      value: attr,
      label: attr
    })) || []
    : [];

  const postTypeOptions: DropdownOption[] = [
    { value: "", label: "All" },
    { value: "Validated", label: "Validated" },
    { value: "Unvalidated", label: "Unvalidated" },
    { value: "Partially Validated", label: "Partially Validated" },
  ];

  const dateFilterOptions: DropdownOption[] = [
    { value: "", label: "All" },
    { value: "daily", label: t("group-posts.daily_posts") },
    { value: "weekly", label: t("group-posts.last_week_posts") },
    { value: "monthly", label: t("group-posts.last_month_posts") },
    { value: "custom", label: t("group-posts.custom_date") },
  ];

  const fetchEntities = async () => {
    try {
      const response = await apiService.get("attributes");
      const attributeData = response as Attribute[];
      const entityMap: Record<string, Set<string>> = {};

      attributeData.forEach(attr => {
        if (!entityMap[attr.Key]) {
          entityMap[attr.Key] = new Set();
        }
        entityMap[attr.Key].add(attr.Value);
      });

      const groupedEntities: Entity[] = Object.entries(entityMap).map(([key, values]) => ({
        type: key,
        attributes: Array.from(values)
      }));

      setEntities(groupedEntities);
    } catch (error) {
      console.error("Error fetching entities:", error);
    }
  };

  const fetchPublications = async () => {
    setLoading(true);
    try {
      const response = await apiService.get<any>("/publications");
      const sortedData = [...response].sort((a, b) => {
        const dateA = new Date(a.Posts?.[0]?.TimeStamp || 0);
        const dateB = new Date(b.Posts?.[0]?.TimeStamp || 0);
        return dateB.getTime() - dateA.getTime();
      });
      setData(sortedData);
      setOriginalData(sortedData);
    } catch (error) {
      console.error("Error fetching publications:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchFilteredData = async (filters: Record<string, any> = {}) => {
    setLoading(true);
    try {
      const filteredParams = Object.fromEntries(
        Object.entries(filters).filter(
          ([_, value]) => value !== undefined && value !== null && value !== ""
        )
      );

      if (filteredParams.startDate) {
        const start = new Date(filteredParams.startDate);
        start.setHours(0, 0, 0, 0);
        filteredParams.startDate = start.toISOString();
      }

      if (filteredParams.endDate) {
        const end = new Date(filteredParams.endDate);
        end.setHours(23, 59, 59, 999);
        filteredParams.endDate = end.toISOString();
      }

      const response = await apiService.getfilter<any[]>("publications", filteredParams);
      setData(response);
      setCurrentPage(1);
    } catch (error) {
      setData([]);
      console.error("Error fetching filtered data:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAlert = async (id: string) => {
    setLoading(true);
    try {
      const response = await apiService.getlocal(`/api/V1/alert/${id}`);
      console.log("Fetched alert:", response);

      const alert = response as {
        Entity?: string;
        Attribute?: string;
        Channel?: string;
        Text?: string;
        WordCount?: number;
        TrackedWords?: string;
        Duration?: string;
        Repeat?: boolean;
        StartDate?: string;
        EndDate?: string;
        PublicationID?: string;
      };

      setConfig({
        scope: alert.Entity ? 'entity' : (alert.PublicationID !== "All" ? 'content' : 'all'),
        entityType: alert.Entity || undefined,
        entityAttribute: alert.Attribute || undefined,
        contentChannel: alert.Channel || 'all',
        contentText: alert.Text || undefined,
        wordCount: alert.WordCount || 10,
        trackedWords: alert.TrackedWords ? alert.TrackedWords.split(',') : [],
        duration: alert.Duration || undefined,
        repeatable: alert.Repeat || false,
        startDate: alert.StartDate ? new Date(alert.StartDate) : undefined,
        endDate: alert.EndDate ? new Date(alert.EndDate) : undefined,
        selectedPublicationId: alert.PublicationID && alert.PublicationID !== "All" ? parseInt(alert.PublicationID) : undefined,
        selectedPublicationText: alert.Text || undefined,
      });
    } catch (error) {
      console.error("Error fetching alert:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      await fetchEntities();
      await fetchPublications();

      if (id) {
        await fetchAlert(id);
      }
    };

    fetchData();
  }, [id]);

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
    }
  };

  const sortData = (field: SortField) => {
    let direction: SortDirection = field === 'date' ? 'desc' : 'asc';

    if (sortField === field) {
      direction = sortDirection === 'asc' ? 'desc' : 'asc';
    }

    setSortField(field);
    setSortDirection(direction);

    if (field === '') {
      setData([...originalData]);
      setCurrentPage(1);
      return;
    }

    const sortedData = [...data].sort((a, b) => {
      if (field === 'text') {
        const textA = a.Posts?.[0]?.Text?.toLowerCase() || '';
        const textB = b.Posts?.[0]?.Text?.toLowerCase() || '';
        return direction === 'asc'
          ? textA.localeCompare(textB)
          : textB.localeCompare(textA);
      } else if (field === 'date') {
        const dateA = new Date(a.Posts?.[0]?.TimeStamp || 0);
        const dateB = new Date(b.Posts?.[0]?.TimeStamp || 0);
        return direction === 'asc'
          ? dateA.getTime() - dateB.getTime()
          : dateB.getTime() - dateA.getTime();
      } else if (field === 'status') {
        const statusA = a.Validation?.toLowerCase() || '';
        const statusB = b.Validation?.toLowerCase() || '';
        return direction === 'asc'
          ? statusA.localeCompare(statusB)
          : statusB.localeCompare(statusB);
      }
      return 0;
    });

    setData(sortedData);
    setCurrentPage(1);
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const term = e.target.value;
    setSearchTerm(term);

    if (term === '') {
      setData(originalData);
    } else {
      const filtered = originalData.filter(post => {
        const searchLower = term.toLowerCase();
        return (
          (post.Posts?.[0]?.Text?.toLowerCase().includes(searchLower)) ||
          (post.Validation?.toLowerCase().includes(searchLower))
        );
      });
      setData(filtered);
    }
    setCurrentPage(1);
  };

  const handleDateFilterChange = (value: string) => {
    setDateFilter(value);
    const today = new Date();
    let start: Date;
    let end: Date = new Date(today);
    end.setHours(23, 59, 59, 999);

    if (value === "daily") {
      start = new Date(today);
      start.setHours(0, 0, 0, 0);
      setStartDate(start.toISOString());
      setEndDate(end.toISOString());
    } else if (value === "weekly") {
      start = new Date(today);
      start.setDate(today.getDate() - 6);
      start.setHours(0, 0, 0, 0);
      setStartDate(start.toISOString());
      setEndDate(end.toISOString());
    } else if (value === "monthly") {
      start = new Date(today);
      start.setDate(today.getDate() - 29);
      start.setHours(0, 0, 0, 0);
      setStartDate(start.toISOString());
      setEndDate(end.toISOString());
    } else {
      setStartDate("");
      setEndDate("");
      setData(originalData);
      setCurrentPage(1);
    }
  };

  const handleFilter = () => {
    fetchFilteredData({
      startDate,
      endDate,
      validation: postType,
    });
    setCurrentPage(1);
  };

  const handleSelectPublication = (publicationId: number, text: string) => {
    setConfig(prev => ({
      ...prev,
      scope: 'content',
      selectedPublicationId: publicationId,
      selectedPublicationText: text,
      contentText: text,
    }));
  };

  const handleRemoveSelection = () => {
    setConfig(prev => ({
      ...prev,
      selectedPublicationId: undefined,
      selectedPublicationText: undefined,
      contentText: undefined,
    }));
  };

  const handleConfigSubmit = async () => {
    const payload = {
      Channel: config.scope === 'content' && config.contentChannel
        ? config.contentChannel
        : 'All',
      Content: config.scope === 'content' && config.selectedPublicationId
        ? config.selectedPublicationId.toString()
        : config.scope === 'content' && config.contentText
          ? config.contentText
          : 'All',
      Entity: config.scope === 'entity' && config.entityType
        ? config.entityType
        : null,
      Attribute: config.scope === 'entity' && config.entityAttribute
        ? config.entityAttribute
        : null,
      Duration: config.duration
        ? parseInt(config.duration.replace(/\D/g, '')) || null
        : null,
      Repeat: config.repeatable,
      StartDate: config.startDate
        ? config.startDate.toISOString()
        : null,
      EndDate: config.endDate
        ? config.endDate.toISOString()
        : null,
      MetricType: 'Mention',
      // WordCount: config.wordCount || null,
      TrackedWords: config.trackedWords.length > 0 ? config.trackedWords.join(',') : null,
      Threshold: null,
      Above: null,
      "#Comments": config.wordCount || null,
      Mention: 'Typed Mention'
    };

    try {
      setLoading(true);
      if (id) {
        await apiService.put(`/api/V1/alert/${id}`, payload);
        alert('Alert updated successfully!');
      } else {
        await apiService.postlocal('/api/V1/alert', payload);
        alert('Alert created successfully!');
      }
      navigate(-1);
    } catch (error) {
      console.error("Error saving alert:", error);
      alert('Error saving alert. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getAlertDescription = () => {
    const isSpecificWord = config.trackedWords && config.trackedWords.length > 0;
    const hasWordCount = !!config.wordCount;
    const durationLabel = durationOptions.find(d => d.value === config.duration)?.label;

    if (!hasWordCount) return ''; // if no wordCount defined, no alert description

    const base = isSpecificWord ? 'A specific word' : 'Any word';
    const countPart = `mentioned more than ${config.wordCount} times`;
    const durationPart = durationLabel ? ` during ${durationLabel}` : '';
    const scope = 'for all posts, a specific content, or a post';

    return `${base} ${countPart}${durationPart} ${scope}`;
  };


  const handleScopeChange = (scope: 'all' | 'entity' | 'content') => {
    setConfig(prev => ({
      ...prev,
      scope,
      entityType: scope === 'entity' ? prev.entityType : undefined,
      entityAttribute: scope === 'entity' ? prev.entityAttribute : undefined,
      contentChannel: scope === 'content' ? prev.contentChannel : undefined,
      contentText: scope === 'content' ? prev.contentText : undefined,
      selectedPublicationId: scope === 'content' ? prev.selectedPublicationId : undefined,
      selectedPublicationText: scope === 'content' ? prev.selectedPublicationText : undefined,
    }));
  };

  const handleEntityTypeChange = (type: string) => {
    const selectedEntity = entities.find(e => e.type.toLowerCase() === type);
    setConfig(prev => ({
      ...prev,
      entityType: type,
      entityAttribute: selectedEntity?.attributes[0]
    }));
  };

  const handleContentChannelChange = (channel: string) => {
    setConfig(prev => ({
      ...prev,
      contentChannel: channel
    }));
  };

  const handleWordCountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (!isNaN(value) && value >= 0) {
      setConfig(prev => ({
        ...prev,
        wordCount: value
      }));
    }
  };

  const handleTrackedWordKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && trackedWordInput.trim()) {
      e.preventDefault();
      if (!config.trackedWords.includes(trackedWordInput.trim())) {
        setConfig(prev => ({
          ...prev,
          trackedWords: [...prev.trackedWords, trackedWordInput.trim()]
        }));
        setTrackedWordInput("");
      }
    }
  };

  const handleDeleteTrackedWord = (wordToDelete: string) => {
    setConfig(prev => ({
      ...prev,
      trackedWords: prev.trackedWords.filter(word => word !== wordToDelete)
    }));
  };

  const handleDurationChange = (duration: string) => {
    setConfig(prev => ({
      ...prev,
      duration: duration || undefined
    }));
  };

  const handleRepeatableChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setConfig(prev => ({
      ...prev,
      repeatable: e.target.checked
    }));
  };

  const handleStartDateChange = (date: Date | null) => {
    setConfig(prev => ({
      ...prev,
      startDate: date || undefined
    }));
  };

  const handleEndDateChange = (date: Date | null) => {
    setConfig(prev => ({
      ...prev,
      endDate: date || undefined
    }));
  };

  // const handleCancel = () => {
  //   navigate(-1);
  // };

  if (loading) {
    return <Loader />;
  }

  return (
    <div className="space-y-6 p-4 rounded-lg bg-white mx-auto">
      <h3 className="text-lg font-medium">
        {id ? t('alertConfig.editAlertConfiguration') : t('alertConfig.newAlertConfiguration')}
      </h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Dropdown
          label={t('alertConfig.contentChannel')}
          value={config.contentChannel || 'all'}
          options={channelOptions}
          onChange={handleContentChannelChange}
          className="w-full"
        />
      </div>

      <div className="space-y-4">
        <h4 className="font-medium">{t('alertConfig.alertScope')}</h4>

        <div className="flex flex-wrap gap-2 justify-center md:justify-start">
          <ScopeButton
            onClick={() => handleScopeChange('all')}
            variant="contained"
            sx={{
              backgroundColor: config.scope === 'all' ? 'primary.light' : 'grey.100',
              color: config.scope === 'all' ? 'primary.contrastText' : 'text.secondary',
              '&:hover': {
                backgroundColor: config.scope === 'all' ? 'primary.main' : 'grey.300',
              },
            }}
          >
            {t('alertConfig.allContent')}
          </ScopeButton>
          <ScopeButton
            onClick={() => handleScopeChange('entity')}
            variant="contained"
            sx={{
              backgroundColor: config.scope === 'entity' ? 'primary.light' : 'grey.100',
              color: config.scope === 'entity' ? 'primary.contrastText' : 'text.secondary',
              '&:hover': {
                backgroundColor: config.scope === 'entity' ? 'primary.main' : 'grey.300',
              },
            }}
          >
            {t('alertConfig.entity')}
          </ScopeButton>
          <ScopeButton
            onClick={() => handleScopeChange('content')}
            variant="contained"
            sx={{
              backgroundColor: config.scope === 'content' ? 'primary.light' : 'grey.100',
              color: config.scope === 'content' ? 'primary.contrastText' : 'text.secondary',
              '&:hover': {
                backgroundColor: config.scope === 'content' ? 'primary.main' : 'grey.300',
              },
            }}
          >
            {t('alertConfig.content')}
          </ScopeButton>
        </div>

        {config.scope === 'entity' && (
          <div className="space-y-4 mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Dropdown
                label={t('alertConfig.entityType')}
                value={config.entityType || ''}
                options={entityTypeOptions}
                onChange={handleEntityTypeChange}
                className="w-full"
              />
              {config.entityType && (
                <Dropdown
                  label={t('alertConfig.entityAttribute')}
                  value={config.entityAttribute || ''}
                  options={attributeOptions}
                  onChange={(val) => setConfig(prev => ({ ...prev, entityAttribute: val }))}
                  className="w-full"
                />
              )}
            </div>
          </div>
        )}

        {config.scope === 'content' && (
          <div className="space-y-4 mt-4">
            <div className="w-full mb-4 px-4">
              <div className="relative w-full">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <IoIosSearch className="text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder={t("Search Posts") || "Search posts..."}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-3xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  value={searchTerm}
                  onChange={handleSearch}
                />
              </div>
            </div>

            <div className="flex flex-col sm:flex-row sm:flex-wrap gap-4 w-full px-4">
              <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px]">
                <label htmlFor="date-filter" className="text-[#5A5A5A] mb-2 block">
                  {t("group-posts.date_filter")}
                </label>
                <Dropdown
                  value={dateFilter}
                  onChange={handleDateFilterChange}
                  options={dateFilterOptions}
                  placeholder="Select time frame"
                  className="rounded-3xl h-[47px] w-full"
                />
              </div>

              {dateFilter === "custom" && (
                <>
                  <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px]">
                    <label htmlFor="start-date" className="text-[#5A5A5A] mb-2 block">
                      {t("group-posts.begin")}
                    </label>
                    <input
                      id="start-date"
                      type="date"
                      value={startDate}
                      onChange={(e) => setStartDate(e.target.value)}
                      className="w-full rounded-lg border border-gray-200 bg-white px-3 h-[47px] py-2 text-gray-900"
                    />
                  </div>

                  <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px]">
                    <label htmlFor="end-date" className="text-[#5A5A5A] mb-2 block">
                      {t("group-posts.end")}
                    </label>
                    <input
                      id="end-date"
                      type="date"
                      value={endDate}
                      onChange={(e) => setEndDate(e.target.value)}
                      className="w-full rounded-lg border border-gray-200 bg-white px-3 h-[47px] py-2 text-gray-900"
                    />
                  </div>
                </>
              )}

              <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px]">
                <label htmlFor="type" className="text-[#5A5A5A] mb-2 block">
                  {t("group-posts.type")}
                </label>
                <Dropdown
                  value={postType}
                  onChange={setPostType}
                  options={postTypeOptions}
                  placeholder="Select post type"
                  className="rounded-3xl h-[47px] w-full"
                />
              </div>
              <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px] flex items-end">
                <button
                  onClick={handleFilter}
                  className="bg-secondary w-full flex items-center justify-center gap-2 text-[#ffffff] py-2 px-10 text-lg h-[47px] font-light rounded-xl"
                >
                  <MdFilterList />
                  {t("group-posts.button")}
                </button>
              </div>
            </div>

            <div className="mt-6 rounded-xl px-4">
              <div className="bg-white overflow-hidden">
                <div className="overflow-x-auto relative">
                  <div className="max-h-[300px] overflow-y-auto">
                    <table className="min-w-full border-collapse text-left text-sm">
                      <thead className="sticky top-0 bg-white z-[1]">
                        <tr className="border-b border-gray-200 font-bold tracking-tight bg-[#EDF1F575] text-sm">
                          <th className="left-col">{t("thumbnail")}</th>
                          <th className="p-4" onClick={() => sortData('text')}>
                            <div className="flex items-center">
                              {t("table.columns.name")}
                              <span className="ml-1 text-xs leading-none pl-1">
                                {sortField === 'text' ? (sortDirection === 'asc' ? '↑' : '↓') : '↑↓'}
                              </span>
                            </div>
                          </th>
                          <th className="p-4">{t("table.columns.email")}</th>
                          <th className="p-4" onClick={() => sortData('date')}>
                            <div className="flex items-center">
                              {t("createdTime")}
                              {sortField === 'date' && (
                                <span className="ml-1 text-xs leading-none pl-1">
                                  {sortDirection === 'asc' ? '↑' : '↓'}
                                </span>
                              )}
                              {!sortField && <span className="ml-1 text-xs leading-none pl-1">↓</span>}
                            </div>
                          </th>
                          <th className="pr-6 pl-4 py-4">{t("table.columns.actions")}</th>
                        </tr>
                      </thead>
                      <tbody>
                        {loading ? (
                          <tr>
                            <td colSpan={6}>
                              <Loader />
                            </td>
                          </tr>
                        ) : paginatedData.length > 0 ? (
                          paginatedData.map((item: any) => (
                            <tr
                              key={item.PublicationID}
                              className={`border-b border-gray-100 hover:bg-gray-50 ${config.selectedPublicationId === item.PublicationID ? 'bg-blue-50' : ''
                                }`}
                            >
                              <td className="left-col">
                                {item.Posts?.[0]?.Thumbnail && (
                                  <img
                                    src={item.Posts[0].Thumbnail || thumbnail}
                                    alt="Thumbnail"
                                    className="w-12 h-12 rounded-xl cursor-pointer"
                                    onClick={() => handleSelectPublication(item.PublicationID, item.Posts?.[0]?.Text || '')}
                                  />
                                )}
                              </td>
                              <td
                                className="p-4 max-w-[150px] text-[0.85rem] text-nowrap truncate lg:min-w-[310px] cursor-pointer"
                                title={item?.Posts?.[0]?.Text || "N/A"}
                                onClick={() => handleSelectPublication(item.PublicationID, item.Posts?.[0]?.Text || '')}
                              >
                                {item?.Posts?.[0]?.Text || "N/A"}
                              </td>
                              <td className="p-4">
                                <div className="flex items-center space-x-0.5">
                                  {item.Posts?.map((post: any, i: number) => {
                                    let icon;
                                    switch (post.SocialNetwork) {
                                      case "Facebook":
                                        icon = <FaFacebookF className="text-[1.78rem] bg-[#1877F2] p-1.5 text-[#fff] rounded-lg" style={{ boxShadow: "0px 3px 10px 0px #1976D252" }} />;
                                        break;
                                      case "Instagram":
                                        icon = <FaInstagram className="text-[1.78rem] p-1.5 text-white rounded-lg" style={{ background: "linear-gradient(45deg, #feda75, #fa7e1e, #d62976, #962fbf, #4f5bd5)", boxShadow: "0px 4px 4px 0px rgba(0, 0, 0, 0.2)" }} />;
                                        break;
                                      case "LinkedIn":
                                        icon = <img src={linkedinIcon} alt="LinkedIn" className="h-6 w-6" />;
                                        break;
                                      case "Twitter":
                                        icon = <FaXTwitter className="text-[1.78rem] bg-[#000] p-1.5 text-[#fff] rounded-lg" style={{ boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.2)" }} />;
                                        break;
                                      case "Youtube":
                                        icon = <RiYoutubeFill className="text-[1.78rem] bg-[#E21A20] p-1.5 text-[#fff] rounded-lg" style={{ boxShadow: "0px 4px 4px 0px #E21A204F" }} />;
                                        break;
                                      default:
                                        return null;
                                    }
                                    return (
                                      <a key={i} href={post.URL} target="_blank" rel="noopener noreferrer">
                                        <span className="h-4 w-4">{icon}</span>
                                      </a>
                                    );
                                  })}
                                </div>
                              </td>
                              <td className="p-4 text-sm">
                                {new Date(item.Posts[0].TimeStamp).toLocaleDateString()}
                              </td>
                              <td className="right-col">
                                <div className="flex items-center gap-1">
                                  <button
                                    className={`px-2 py-2 rounded-lg text-sm cursor-pointer text-[#21CE9E] ${config.selectedPublicationId === item.PublicationID ? 'bg-[#21CE9E] text-white' : 'bg-[#21CE9E1A]'
                                      }`}
                                    onClick={() => handleSelectPublication(item.PublicationID, item.Posts?.[0]?.Text || '')}
                                  >
                                    {item.Validation === "Validated" ? <X size={20} /> : <Check size={20} />}
                                  </button>
                                  {config.selectedPublicationId === item.PublicationID && (
                                    <button
                                      className="px-2 py-2 rounded-lg text-sm cursor-pointer text-[#FB4242] bg-[#FB42421A]"
                                      onClick={handleRemoveSelection}
                                    >
                                      <X size={20} />
                                    </button>
                                  )}
                                </div>
                              </td>
                            </tr>
                          ))
                        ) : (
                          <tr>
                            <td colSpan={6} className="text-center py-4">
                              No data available
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                  {!loading && (
                    <div className="mt-4 px-8">
                      <Pagination
                        currentPage={currentPage}
                        totalItems={data.length}
                        itemsPerPage={itemsPerPage}
                        onPageChange={handlePageChange}
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>

            {config.selectedPublicationId && (
              <div className="p-4 bg-blue-50 rounded-md border border-blue-200">
                <div className="flex justify-between items-center">
                  <div>
                    <h4 className="font-medium text-blue-800">Selected Publication</h4>
                    <p className="text-sm text-blue-600">
                      ID: {config.selectedPublicationId}
                    </p>
                    {config.selectedPublicationText && (
                      <p className="text-sm mt-1 text-gray-700 line-clamp-2">
                        {config.selectedPublicationText}
                      </p>
                    )}
                  </div>
                  <button
                    onClick={handleRemoveSelection}
                    className="text-red-500 hover:text-red-700"
                  >
                    <X size={20} />
                  </button>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      <div className="space-y-4">
        <h4 className="font-medium">{t('alertConfig.metrics')}</h4>

        <div className="p-4 bg-gray-50 rounded-md">
          <h4 className="font-medium mb-2">Alert Description:</h4>
          <p className="text-gray-700">{getAlertDescription()}</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-end">
          <div className="w-full flex flex-col justify-end">
            <label className="block text-sm font-medium mb-1 text-gray-700">
              Word Count (minimum)
            </label>
            <input
              type="number"
              min="10"
              value={config.wordCount}
              onChange={handleWordCountChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 h-[42px]"
            />
          </div>
        </div>

        <div className="w-full">
          <label className="block text-sm font-medium mb-1 text-gray-700">
            Tracked Words
          </label>
          <TextField
            fullWidth
            variant="outlined"
            value={trackedWordInput}
            onChange={(e) => setTrackedWordInput(e.target.value)}
            onKeyDown={handleTrackedWordKeyDown}
            placeholder="Type a word and press Enter"
            InputProps={{
              startAdornment: (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {config.trackedWords.map((word, index) => (
                    <Chip
                      key={index}
                      label={word}
                      onDelete={() => handleDeleteTrackedWord(word)}
                      size="small"
                    />
                  ))}
                </Box>
              ),
            }}
          />
          <p className="text-xs text-gray-500 mt-1">
            Add words to track in the content. Press Enter after each word.
          </p>
        </div>
      </div>

      <div className="space-y-4">
        <h4 className="font-medium">{t('alertConfig.alertCharacteristics')}</h4>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Dropdown
            label={t('alertConfig.duration')}
            value={config.duration || ''}
            options={durationOptions}
            onChange={handleDurationChange}
            className="w-full"
          />

          <FormControlLabel
            control={
              <Switch
                checked={config.repeatable}
                onChange={handleRepeatableChange}
                color="primary"
              />
            }
            label={t('alertConfig.repeatable')}
            className="items-center"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">
              {t('alertConfig.startDate')}
            </label>
            <DatePicker
              selected={config.startDate}
              onChange={handleStartDateChange}
              minDate={new Date()}
              className="w-full p-2 border rounded"
              placeholderText={t('alertConfig.selectDate')}
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">
              {t('alertConfig.endDate')}
            </label>
            <DatePicker
              selected={config.endDate}
              onChange={handleEndDateChange}
              minDate={config.startDate || new Date()}
              className="w-full p-2 border rounded"
              placeholderText={t('alertConfig.selectDate')}
            />
          </div>
        </div>
      </div>

      <div className="flex gap-4">
        <ActionButton
          onClick={handleConfigSubmit}
          fullWidth
          size="large"
          sx={{ mt: 3 }}
        >
          {t('alertConfig.saveAlert')}
        </ActionButton>
      </div>
    </div>
  );
};

export default AlertScopeConfig;