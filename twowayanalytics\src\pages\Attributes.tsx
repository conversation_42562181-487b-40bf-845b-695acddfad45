import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import apiService from "../services/apiService";
import Loader from "./loader";
import { CirclePlus, SquarePen } from "lucide-react";
import { GoPencil, GoTrash } from "react-icons/go";
import Dropdown from "../components/Dropdown";
import Modal from "../components/Modal";
import Pagination from "../components/Pagination";

interface Attribute {
  id: string;
  AttributeId: string;
  Key: string;
  Value: string;
  EntityId: string;
  Entity: string;
  thumbnail?: string;
}

interface Entity {
  EntityId: string;
  Entity: string;
}

function SettingsAttributes() {
  const { t } = useTranslation();
  const [data, setData] = useState<Attribute[]>([]);
  const [filteredData, setFilteredData] = useState<Attribute[]>([]);
  const [loading, setLoading] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [attribute, setAttribute] = useState("");
  const [editingAttributeId, setEditingAttributeId] = useState<string | null>(null);
  const [entities, setEntities] = useState<Entity[]>([]);
  const [entityName, setSelectedEntity] = useState("");
  const [selectedEntityId, setSelectedEntityId] = useState("");
  const [currentUploadingAttributeId, setCurrentUploadingAttributeId] = useState<string | null>(null);
  const [selectedFilterEntityId, setSelectedFilterEntityId] = useState<string>("");
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [itemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");

  const [filteredByEntityData, setFilteredByEntityData] = useState<any[]>(data); // stores entity-filtered data


  // Update pagination calculations to use filteredData
  const totalItems = filteredData.length;
  const pageSize = itemsPerPage;
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = Math.min(startIndex + pageSize, totalItems);
  const paginatedData = filteredData.slice(startIndex, endIndex);

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= Math.ceil(filteredData.length / itemsPerPage)) {
      setCurrentPage(newPage);
    }
  };

  const fetchData = async () => {
    setLoading(true);
    try {
      const response = await apiService.get("attributes");
      setData(response as Attribute[]);
      setFilteredData(response as Attribute[]);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchEntities = async () => {
    try {
      const response = await apiService.get("entities");
      setEntities(response as Entity[]);
    } catch (error) {
      console.error("Error fetching entities:", error);
    }
  };

  const handleEntityFilter = (entityId: string | number) => {
    const idStr = String(entityId);
    setSelectedFilterEntityId(idStr);
    setCurrentPage(1);

    if (!entityId) {
      setFilteredByEntityData(data); // Reset entity filter
      const regex = new RegExp(`\\b${searchTerm}\\b`, 'i');
      const finalFiltered = data.filter((item) => regex.test(item.Value));
      setFilteredData(finalFiltered);
      return;
    }

    const entityFiltered = data.filter(
      (item) => String(item.EntityId) === idStr
    );
    setFilteredByEntityData(entityFiltered);

    // Apply search on filtered entity data
    const regex = new RegExp(`\\b${searchTerm}\\b`, 'i');
    const finalFiltered = entityFiltered.filter((item) =>
      regex.test(item.Value)
    );
    setFilteredData(finalFiltered);
  };


  // const handleEntityFilter = (entityId: string | number) => {
  //   const idStr = String(entityId);
  //   setSelectedFilterEntityId(idStr);
  //   setCurrentPage(1);

  //   if (!entityId) {
  //     setFilteredByEntityData(data); // Reset entity filter
  //     const finalFiltered = data.filter((item) =>
  //       item.Value.toLowerCase().includes(searchTerm)
  //     );
  //     setFilteredData(finalFiltered);
  //     return;
  //   }

  //   const entityFiltered = data.filter(
  //     (item) => String(item.EntityId) === idStr
  //   );

  //   setFilteredByEntityData(entityFiltered);

  //   // Apply search on filtered entity data
  //   const finalFiltered = entityFiltered.filter((item) =>
  //     item.Value.toLowerCase().includes(searchTerm)
  //   );
  //   setFilteredData(finalFiltered);
  // };

  const handleAddAttribute = () => {
    setShowForm(true);
    setEditingAttributeId(null);
    setAttribute("");
    setSelectedEntity("");
    setSelectedEntityId("");
  };

  const handleEdit = (
    id: string,
    attributeValue: string,
    entityName: string,
    entityId: string
  ) => {
    setEditingAttributeId(id);
    setAttribute(attributeValue);
    setSelectedEntity(entityName);
    setSelectedEntityId(entityId);
    setShowForm(true);
  };

  const handleSubmit = async () => {
    if (!attribute.trim() || !selectedEntityId) {
      alert(t("SettingsAttributes.fillAllFields"));
      return;
    }

    setLoading(true);
    try {
      if (editingAttributeId) {
        await apiService.put(`attributes/${editingAttributeId}`, {
          value: attribute,
          key: entityName,
          entityId: selectedEntityId,
        });
      } else {
        await apiService.post("attributes", {
          value: attribute,
          key: entityName,
          entityId: selectedEntityId,
        });
      }
      await fetchData();
      setShowForm(false);
      setAttribute("");
      setSelectedEntity("");
      setSelectedEntityId("");
    } catch (error) {
      console.error("Error saving attribute:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm(t("SettingsAttributes.confirmDelete"))) return;

    setLoading(true);
    try {
      await apiService.delete(`/attributes/${id}`);
      await fetchData();
    } catch (error) {
      console.error("Error deleting attribute:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectEntity = (selectedId: string | number) => {
    const selected = entities.find(
      (entity) => String(entity.EntityId) === String(selectedId)
    );

    if (selected) {
      setSelectedEntity(selected.Entity);
      setSelectedEntityId(selected.EntityId);
    } else {
      setSelectedEntity("");
      setSelectedEntityId("");
    }
  };

  const handleImageClick = (attributeId: string) => {
    setCurrentUploadingAttributeId(attributeId);
    fileInputRef.current?.click();
  };

  const handleImageSelect = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];

    if (!file || !currentUploadingAttributeId) return;

    if (!file.type.startsWith("image/")) {
      alert("Please upload a valid image file.");
      return;
    }

    const maxSizeInBytes = 4 * 1024 * 1024;
    if (file.size > maxSizeInBytes) {
      alert("Image size should not exceed 4 MB.");
      return;
    }

    await handleUploadImage(currentUploadingAttributeId, file);
    setCurrentUploadingAttributeId(null);
  };

  //  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   const term = e.target.value.toLowerCase();
  //   setSearchTerm(term);
  //   setCurrentPage(1); 
  //   const filtered = data.filter((item) =>
  //     item.Value.toLowerCase().includes(term)
  //   );
  //   setFilteredData(filtered);
  // };


  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const term = e.target.value.toLowerCase();
    setSearchTerm(term);
    setCurrentPage(1);

    const base = selectedFilterEntityId ? filteredByEntityData : data;

    const searched = base.filter((item) =>
      item.Value.toLowerCase().includes(term)
    );

    setFilteredData(searched);
  };


  const handleUploadImage = async (attributeId: string, imageFile: File) => {
    setLoading(true);
    try {
      const formData = new FormData();
      formData.append("image", imageFile, imageFile.name);

      const response = await apiService.putimage(
        `/attributes/${attributeId}/image`,
        formData,
        {
          accept: "*/*",
          "Content-Disposition": `attachment; filename="${imageFile.name}"`,
        }
      );

      if (response) {
        await fetchData();
      }
    } catch (error) {
      console.error("Error uploading image:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
    fetchEntities();
  }, []);

  return (
    <div className="container-fluid xs:max-sm:px-2">
      <div className="flex flex-col xs:max-sm:gap-2 sm:flex-row sm:items-center sm:gap-6 my-4 px-4">
        <h1 className="pl-2 font-bold text-xl xs:max-sm:text-lg xs:max-sm:max-w-full">
          {t("SettingsAttributes.title")}
        </h1>

        <div className="flex flex-col xs:max-sm:gap-2 sm:flex-row sm:items-center sm:gap-4 w-full sm:w-auto sm:ml-auto">
          <input
            type="text"
            placeholder={t("SettingsAttributes.attributeSearchPlaceholder")}
            value={searchTerm}
            onChange={handleSearch}
            className="bg-white border border-gray-200 outline-none rounded-lg px-3 py-2 w-full sm:w-64 lg:w-80 xs:max-sm:text-sm"
          />

          <Dropdown
            value={selectedFilterEntityId}
            onChange={handleEntityFilter}
            options={[
              { value: "", label: t("SettingsAttributes.allEntities") },
              ...entities.map((entity) => ({
                value: entity.EntityId,
                label: entity.Entity,
              })),
            ]}
            placeholder={t("SettingsAttributes.filterByEntity")}
            className="w-full sm:w-64 lg:w-80 border border-gray-200 rounded-lg"
          />

          <button
            className="bg-primary text-sm font-semibold flex items-center justify-center gap-2 text-white py-3 px-4 rounded-xl w-full sm:w-auto xs:max-sm:text-xs"
            onClick={handleAddAttribute}
          >
            {t("SettingsAttributes.addAttribute")}
          </button>
        </div>
      </div>


      <Modal
        isOpen={showForm}
        onClose={() => {
          setShowForm(false);
        }}
        label={
          editingAttributeId
            ? t("SettingsAttributes.updateAttribute")
            : t("SettingsAttributes.selectEntity")
        }
      >
        <div className="gap-4 mt-8">
          <div className="flex flex-col gap-2 mb-2">
            <label className="font-medium">
              {t("SettingsAttributes.enterAttribute")}
            </label>
            <input
              className="border border-gray-200 outline-none rounded-lg px-3 py-3 w-full form-control"
              type="text"
              placeholder={t("SettingsAttributes.enterAttribute")}
              value={attribute}
              onChange={(e) => setAttribute(e.target.value)}
            />
          </div>
          <Dropdown
            label={t("SettingsAttributes.selectEntity")}
            value={selectedEntityId ?? ""}
            onChange={handleSelectEntity}
            disabled={!!editingAttributeId}
            options={entities.map((entity) => ({
              value: entity.EntityId,
              label: entity.Entity,
            }))}
            placeholder={t("SettingsAttributes.selectEntity")}
          />

          <button
            className="bg-primary w-full mt-6 flex items-center py-3 px-4 justify-center gap-2 btn text-white text-md font-extralight rounded-xl"
            onClick={handleSubmit}
          >
            {editingAttributeId ? (
              <SquarePen className="font-lighter" size={20} />
            ) : (
              <CirclePlus className="font-lighter" size={20} />
            )}
            {editingAttributeId
              ? t("settingsEntitie.update")
              : t("settingsEntitie.addButton")}
          </button>
        </div>
      </Modal>

      <div className="mt-6 bg-gray-100">
        <div className="bg-white rounded-2xl overflow-x-auto">
          <table className="min-w-full border-collapse text-left">
            <thead>
              <tr className="border-b border-gray-100 font-bold tracking-tight bg-[#F3FAFD] text-sm">
                <th className="py-4 pl-6 pr-4 xs:max-sm:px-0 xs:max-sm:pl-2 xs:max-sm:text-[12px]">
                  {t("SettingsAttributes.entityKey")}
                </th>
                <th className="p-4 xs:max-sm:px-0 xs:max-sm:text-[12px]">
                  {t("SettingsAttributes.entityName")}
                </th>
                <th className="p-4 xs:max-sm:px-0 xs:max-sm:text-[12px]">
                  {t("SettingsAttributes.image")}
                </th>
                <th className="py-4 pl-4 pr-6 xs:max-sm:px-0 xs:max-sm:text-[12px]">
                  {t("SettingsAttributes.actions")}
                </th>
              </tr>
            </thead>
            <tbody>
              {loading && !paginatedData.length ? (
                <tr>
                  <td colSpan={4} className="p-4">
                    <Loader />
                  </td>
                </tr>
              ) : paginatedData.length > 0 ? (
                paginatedData.map((item) => (
                  <tr
                    key={item.AttributeId}
                    className="border-b border-gray-100 hover:bg-gray-50"
                  >
                    <td className="py-4 pl-6 pr-4 xs:max-sm:px-2">{item.Key}</td>
                    <td className="p-4">{item.Value}</td>
                    <td className="p-4">
                      <div className="relative w-16 xs:max-sm:h-10 h-16 xs:max-sm:w-10 group">
                        <img
                          src={`${item.thumbnail}?t=${Date.now()}`}
                          alt={item.AttributeId || "Thumbnail"}
                          className="w-12 h-12 object-cover rounded-md"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.onerror = null;
                            target.src =
                              "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANkAAACUCAMAAAAppnz2AAAAQlBMVEX///+8vb/v8PC1trnKy8zGyMf6+/rj5OS5u77///319fbHx8nHx8u6vsG5u7y8vcHU1dbe3t7q6uu1tbTa2t2+wb80IpWwAAAE80lEQVR4nO2d2ZajIBBAg9qGYBqXMf//q6O4BBewQCBlmvsyL4nhHqiiWOy53SKRSCQSiUQwkF+TY7Hk9XM9nsUrOTZLyRX5F80uh97scWGzFGxGeQ9FT9dIM7Oivl+F+mliRp+A+QEJrKAmZgUL0ywHfKnZ4/H4UrOuzYZmz6uYGY/GaIaAaBbN8BDNohkeolk0w0M0i2Z4iGYfNMtZh/nXsJsl9yrLiiKr6sbwm7jNWPaadjc5+bmbfRexGatSQt/bvTTNTH4dsVmSrTfcOTEYknjNupaV6116SuAjEq9ZQddeguNToxG0ZtX+2Q98xxOrWbPr1Te1Aj4BqVme7Y/Fvg3AQxOkZsou6xJkDXsEUrOaq9VK2CNwmuWKxDgAS49IzTRehMPmNJxmTHfcDww0nGbaiwyXNvvePvtes5xosj6HFfxIzTKN2QtWhOA0083UNIM9AqlZ8lKapcDVJ1KzW6XqNEqAT8BqlpzMH3jNbvf9ypFDl2d4zfYX1TwDX2nDa5bvhBo3uB2F1+x2aym17jG8Zo/+R5NM2kmlvDDaJA5s9hAtBn1Q/HOvCiLu93KS1Wa3RoOasQSuNpInTV1Xbd0kprdhQ5rlP6/kZmhmT0AzVnBKwVu8pwln1on16S2YWjAzIfb7S0vTEz77HwxjlhfjvEvLQL0WyExaJFMaptfCmDF5ZzRQGglixoplCRgk1kKYiRj7lcyCxFoAMxFjv7JZkFjzbzYMxYVYkFjzbsae+4vjLtb81lm+zfJCsVXjPdY8m2k2eyl4r8YOv2ZsnRVluN8a0qsZyzZZcWH26nvN17LGp9mi8tgdkKQxXooa/roXM+2R7KhGmwv22bqkUgxJb7HmzYzpDoqWA9IPvsy0WVGm9FUeezLLX7qsGKTX/JjBYmxSg5fHjWgKLOd4MTMSM+i1Nq0fU3sAjXBvdjiPWaq1lNAaPEd4MBOXpkAhNjNUIwe03UAoP3l+xvpTBljykNSOy+O2a0DZ39wE9ppzM/azs9AEmA2FlrrV7TTC+TggjwRdmxkmjzf9FitEjIyxdpggHZtZixH9jtbi/JOCYs2tGdPdKjqEKxc17fLMGnRL2qlZUhDzEJP6op+yd8zydj2LpO1xGnFpJp5lmhUlymG9tnluL7Z63YIe35dzaHYmxqYGk2Z74tvuPXZU0+QRd2bnYmxq8HYpqnjZIq3yQGYOemxSk8l3e6znqBpxZeZIjIzJf+6LTfKQ1bSH8o7MmHmtqGKMtbfY5l2t+ZPaNOLGjFHwQhOgxudYG2tFFamu15yYJc6G4qA2xpo6xt5q6qWoCzMxQbtFxFp7/Dnezg30YKZ/icUKEWua5CF9slW1+rxZnjodimODeaOYx9YoY+20GXj7zVCtn/c1yUNW25+tz5olJ2tFB0xT9krwpJmHGDOHVn1yXJeb58zYp6UGhmrEZZ85KYJdsFdDnjFLMAzFAb5dip4w85QV7djujdibsdJm+80bm3nN2gxNjE2M2z5zhrQ1c7cec8YQa3O8WZoZH0qEYBlrdmZiHkMTYjOLVbaVGesLus+WVPvIacTm7xE7Xmi6RKohLcwQTdBbaDUdZ5iboUv3S+ZCy9gMudg71kzNGkwl1T7jUtTMjBBx2oLbjNKq7mjFvhPc7BKI/+1hSHNfZiYRza7H3zYrsitShHtJMRKJRCKRSORP8h+ptHV/XdGNDgAAAABJRU5ErkJggg==";
                          }}
                        />
                        <button
                          type="button"
                          onClick={() => handleImageClick(item.AttributeId)}
                          className="absolute top-0 right-0 m-1 p-1 bg-white rounded-full shadow-md hover:bg-gray-100 transition-opacity opacity-0 group-hover:opacity-100"
                          title="Change Image"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            strokeWidth={2}
                            stroke="currentColor"
                            className="w-4 h-4 text-gray-600"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="M15.232 5.232l3.536 3.536M9 13l6-6m2.121-2.121a1.5 1.5 0 112.121 2.121L9 17.25H5.25V13L17.121 1.879z"
                            />
                          </svg>
                        </button>
                        <input
                          type="file"
                          accept="image/*"
                          onChange={handleImageSelect}
                          className="hidden"
                          ref={fileInputRef}
                        />
                      </div>
                    </td>
                    <td className="py-4 pr-6 pl-4">
                      <div className="flex items-center gap-3">
                        <button
                          className="px-3 py-3 rounded-xl bg-[#21CE9E1A] cursor-pointer text-xl text-[#21CE9E]"
                          onClick={() =>
                            handleEdit(
                              item.AttributeId,
                              item.Value,
                              item.Key,
                              item.EntityId
                            )
                          }
                        >
                          <GoPencil />
                        </button>
                        <button
                          className="px-3 py-3 rounded-xl cursor-pointer text-[#FB4242] bg-[#FB42421A]"
                          onClick={() => handleDelete(item.AttributeId)}
                        >
                          <GoTrash />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4} className="text-center py-4">
                    {t("SettingsAttributes.noDataAvailable")}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
          <div className="mt-4 px-8">
            <Pagination
              currentPage={currentPage}
              totalItems={filteredData.length}
              itemsPerPage={itemsPerPage}
              onPageChange={handlePageChange}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default SettingsAttributes;