import { Request, Response } from "express";
import { sql, poolPromise } from "../config/db";
import dotenv from "dotenv";
import fs from "fs";

dotenv.config();

/**
 * @swagger
 * tags:
 *   name: Attributes
 *   description: API for managing attributes
 */

/**
 * Check if an attribute ID exists in the `Attributes` table
 */
const checkAttributeExists = async (id: number): Promise<boolean> => {
    try {
        const pool = await poolPromise;
        const result = await pool.request()
            .input("id", sql.Int, id)
            .query(`SELECT 1 FROM ${process.env.CLIENT_NAME}.Attributes WHERE AttributeId = @id AND IsDeleted = 0`);
        return result.recordset.length > 0;
    } catch (error) {
        console.error("Error checking attribute existence:", error);
        return false;
    }
};

/**
 * Check if an entity ID exists in the `Entities` table
 */
const checkEntityExists = async (entityId: number): Promise<boolean> => {
    try {
        const pool = await poolPromise;
        const result = await pool.request()
            .input("entityId", sql.Int, entityId)
            .query(`SELECT 1 FROM ${process.env.CLIENT_NAME}.Entities WHERE EntityId = @entityId`);
        return result.recordset.length > 0;
    } catch (error) {
        console.error("Error checking entity existence:", error);
        return false;
    }
};

/**
 * @swagger
 * /api/attributes:
 *   post:
 *     summary: Create a new attribute with optional image upload
 *     tags: [Attributes]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               key:
 *                 type: string
 *               value:
 *                 type: string
 *               entityId:
 *                 type: integer
 *               image:
 *                 type: string
 *                 format: binary
 *     responses:
 *       201:
 *         description: Attribute created successfully
 */
export const createAttribute = async (req: Request, res: Response): Promise<void> => {
    const { key, value, entityId } = req.body;

    if (!key || !value || !entityId) {
        res.status(400).json({ error: "All fields are required" });
        return;
    }

    if (!(await checkEntityExists(entityId))) {
        res.status(400).json({ error: "Invalid entity ID" });
        return;
    }

    try {
        const pool = await poolPromise;
        const result = await pool.request()
            .input("key", sql.NVarChar, key)
            .input("value", sql.NVarChar, value)
            .input("entityId", sql.Int, entityId)
            .query(`INSERT INTO ${process.env.CLIENT_NAME}.Attributes ([Key], Value, EntityId) 
                    OUTPUT Inserted.AttributeId
                    VALUES (@key, @value, @entityId)`);

        const attributeId = result.recordset[0].AttributeId;

        if (req.file) {
            const imagePath = `DataLake/media/${process.env.CLIENT_NAME}/Entities/${attributeId}.jpg`;
            fs.renameSync(req.file.path, imagePath);
        }

        res.status(201).json({ message: "Attribute created successfully", attributeId });
        return;
    } catch (err) {
        console.error("Error creating attribute:", err);
        res.status(500).json({ error: "Server error" });
        return;
    }
};

/**
 * @swagger
 * /api/attributes:
 *   get:
 *     summary: Get all attributes
 *     tags: [Attributes]
 *     responses:
 *       200:
 *         description: List of attributes retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   AttributeId:
 *                     type: integer
 *                   Key:
 *                     type: string
 *                   Value:
 *                     type: string
 *                   EntityId:
 *                     type: integer
 */
export const getAttributes = async (req: Request, res: Response): Promise<void> => {
    try {
        const pool = await poolPromise;
        const result = await pool.request()
            .query(`SELECT AttributeId, [Key], Value, EntityId 
                    FROM ${process.env.CLIENT_NAME}.Attributes 
                    WHERE IsDeleted = 0`);

        res.status(200).json(result.recordset);
        return;
    } catch (err) {
        console.error("Error retrieving attributes:", err);
        res.status(500).json({ error: "Server error" });
        return;
    }
};

/**
 * @swagger
 * /api/attributes/{id}:
 *   get:
 *     summary: Get an attribute by ID
 *     tags: [Attributes]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Attribute retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 AttributeId:
 *                   type: integer
 *                 Key:
 *                   type: string
 *                 Value:
 *                   type: string
 *                 EntityId:
 *                   type: integer
 *       404:
 *         description: Attribute not found
 */
export const getAttributeById = async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;

    if (!id) {
        res.status(400).json({ error: "Attribute ID is required" });
        return;
    }

    try {
        const pool = await poolPromise;
        const result = await pool.request()
            .input("id", sql.Int, id)
            .query(`SELECT AttributeId, [Key], Value, EntityId 
                    FROM ${process.env.CLIENT_NAME}.Attributes 
                    WHERE AttributeId = @id AND IsDeleted = 0`);

        if (result.recordset.length === 0) {
            res.status(404).json({ error: "Attribute not found" });
            return;
        }

        res.status(200).json(result.recordset[0]);
        return;
    } catch (err) {
        console.error("Error retrieving attribute:", err);
        res.status(500).json({ error: "Server error" });
        return;
    }
};



/**
 * @swagger
 * /api/attributes/{id}/image:
 *   post:
 *     summary: Upload an image for an attribute
 *     tags: [Attributes]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               image:
 *                 type: string
 *                 format: binary
 *     responses:
 *       200:
 *         description: Image uploaded successfully
 */
export const uploadAttributeImage = async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;

    if (!req.file) {
        res.status(400).json({ error: "No image uploaded" });
        return;
    }

    if (!(await checkAttributeExists(Number(id)))) {
        res.status(404).json({ error: "Attribute not found" });
        return;
    }

    try {
        const imagePath = `DataLake/media/${process.env.CLIENT_NAME}/Entities/${id}.jpg`;
        fs.renameSync(req.file.path, imagePath);
        
        res.json({ message: "Image uploaded successfully" });
        return;
    } catch (err) {
        console.error("Error uploading image:", err);
        res.status(500).json({ error: "Server error" });
        return;
    }
};


/**
 * @swagger
 * /api/attributes/{id}:
 *   delete:
 *     summary: Soft delete an attribute
 *     tags: [Attributes]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Attribute deleted successfully
 */
export const deleteAttribute = async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;

    if (!id) {
        res.status(400).json({ error: "Attribute ID is required" });
        return;
    }

    if (!(await checkAttributeExists(Number(id)))) {
        res.status(404).json({ error: "Attribute not found" });
        return;
    }

    try {
        const pool = await poolPromise;
        await pool.request()
            .input("id", sql.Int, id)
            .query(`UPDATE ${process.env.CLIENT_NAME}.Attributes SET IsDeleted = 1 WHERE AttributeId = @id`);

        // Delete the image file if it exists
        const imagePath = `DataLake/media/${process.env.CLIENT_NAME}/Entities/${id}.jpg`;
        if (fs.existsSync(imagePath)) {
            fs.unlinkSync(imagePath);
        }

        res.json({ message: "Attribute deleted successfully" });
        return;
    } catch (err) {
        console.error("Error deleting attribute:", err);
        res.status(500).json({ error: "Server error" });
        return;
    }
};

