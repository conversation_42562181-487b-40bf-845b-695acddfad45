import { useEffect, useRef, useState } from "react";

type OptionType = {
  value: string;
  label: string;
};

interface DropdownProps {
  label?: string;
  options: OptionType[];
  value: string | number;
  onChange: (value: string) => void;
  disabled?: boolean;
  className?: string;
  containerClass?: string;
  iconStyles?: string;
  placeholder?: string;
}

const Dropdown: React.FC<DropdownProps> = ({
  label,
  options = [],
  value,
  onChange,
  disabled = false,
  className,
  containerClass,
  iconStyles,
  placeholder = "Select an option",
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const selectedOption = options.find(
    (opt) => String(opt.value) === String(value)
  );

  const handleOptionClick = (e: React.MouseEvent, val: string) => {
    // Stop the event from propagating to parent
    e.stopPropagation();
    onChange(val);
    setIsOpen(false);
  };

  const toggleDropdown = () => {
    if (!disabled) {
      setIsOpen((prev) => !prev);
    }
  };

  return (
    <div
      className={`flex flex-col gap-2 w-full ${containerClass}`}
      ref={dropdownRef}
    >
      {label && <label className="font-medium">{label}</label>}
      <div
        className={`relative border items-center flex border-gray-200 rounded-lg px-2 py-2 w-full cursor-pointer ${disabled ? "bg-gray-100 cursor-not-allowed" : "hover:border-gray-200 bg-white"} ${className}`}
        onClick={toggleDropdown}
      >
        <span className={`text-sm ${!selectedOption ? "text-gray-500" : ""}`}>
          {selectedOption?.label || placeholder}
        </span>
        <svg
          className={`absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-black ${iconStyles}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>

        {isOpen && (
          <div className="absolute left-0 top-full mt-2 w-full border border-gray-200 rounded-lg shadow-lg z-50 max-h-60 overflow-auto">
            {options.map((opt) => (
              <div
                key={opt.value}
                className={`px-4 py-2 hover:bg-gray-100 font-light cursor-pointer ${
                  String(opt.value) === String(value) ? "bg-gray-100" : "bg-white"
                }`}
                onClick={(e) => handleOptionClick(e, opt.value)}
              >
                {opt.label}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Dropdown;
