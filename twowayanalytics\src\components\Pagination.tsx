import { ChevronLeft, ChevronRight } from "lucide-react";
import React from "react";

interface PaginationProps {
  currentPage: number;
  totalItems: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
  showItemCount?: boolean;
  className?: string;
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalItems,
  itemsPerPage,
  onPageChange,
  showItemCount = true,
  className = "",
}) => {
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const start = totalItems === 0 ? 0 : (currentPage - 1) * itemsPerPage + 1;
  const end = Math.min(currentPage * itemsPerPage, totalItems);

  if (totalItems === 0) {
    return null;
  }

  return (
    <div
      className={`flex flex-wrap justify-between items-center gap-4 py-4 ${className}`}
    >
      {/* Summary */}
      {showItemCount && (
        <div className="text-sm text-gray-700">
          Showing <strong>{start}</strong>–<strong>{end}</strong> of{" "}
          <strong>{totalItems}</strong> results
        </div>
      )}

      {/* Navigation */}
      {totalPages > 1 && (
        <div className="flex items-center gap-2 flex-wrap">
          <button
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className={`border bg-lightblue text-primary px-3 py-1 rounded-md flex items-center gap-1 text-sm ${
              currentPage === 1
                ? "cursor-not-allowed text-gray-400 border-gray-200"
                : "hover:bg-gray-100"
            }`}
            aria-label="Previous page"
          >
            <ChevronLeft className="h-4 w-4" />
            <span className="hidden sm:inline">Prev</span>
          </button>

          <div className="px-3 py-1 bg-primary text-white rounded-md text-sm border border-gray-300">
            {currentPage} / {totalPages}
          </div>

          <button
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className={`border border-gray-300 bg-lightblue text-primary px-3 py-1 rounded-md flex items-center gap-1 text-sm ${
              currentPage === totalPages
                ? "cursor-not-allowed text-gray-400 border-gray-200"
                : "hover:bg-gray-100"
            }`}
            aria-label="Next page"
          >
            <span className="hidden sm:inline">Next</span>
            <ChevronRight className="h-4 w-4" />
          </button>
        </div>
      )}
    </div>
  );
};

export default Pagination;
