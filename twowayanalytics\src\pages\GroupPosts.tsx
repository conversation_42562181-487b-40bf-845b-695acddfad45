// import { Check, X } from "lucide-react";
// import React, { useEffect, useState } from "react";
// import { useTranslation } from "react-i18next";
// import { FaFacebookF, FaInstagram, FaXTwitter } from "react-icons/fa6";
// import { GoPencil } from "react-icons/go";
// import { IoIosArrowDown, IoIosSearch } from "react-icons/io";
// import { MdFilterList } from "react-icons/md";
// import { RiYoutubeFill } from "react-icons/ri";
// import linkedinIcon from "../assets/Linkedin.svg";
// import thumbnail from "../assets/thumbnail.svg";
// import Dropdown from "../components/Dropdown";
// import Pagination from "../components/Pagination";
// import SocialNetworkChips from "../components/SocialNetworkChips";
// import apiService from "../services/apiService";
// import AddModal from "./addModal";
// import Editmodal from "./editmodal";
// import Loader from "./loader";

// function GroupPosts() {
//   const { t } = useTranslation();
//   const [expandedRows, setExpandedRows] = useState<{ [key: number]: boolean }>({});
//   const [isModalOpen, setIsModalOpen] = useState(false);
//   const [isAddModalOpen, setIsAddModalOpen] = useState(false);
//   const [selectedPost, setSelectedPost] = useState<any>(null);
//   const [data, setData] = useState<any[]>([]);
//   const [originalData, setOriginalData] = useState<any[]>([]);
//   const [loading, setLoading] = useState(false);
//   const [dateFilter, setDateFilter] = useState(() => localStorage.getItem("groupPostsDateFilter") || "monthly");
//   const [startDate, setStartDate] = useState(() => {
//     const today = new Date();
//     const lastMonth = new Date(today);
//     lastMonth.setDate(today.getDate() - 29);
//     lastMonth.setHours(0, 0, 0, 0);
//     return lastMonth.toISOString().split('T')[0];
//   });
//   const [endDate, setEndDate] = useState(() => {
//     const today = new Date();
//     today.setHours(23, 59, 59, 999);
//     return today.toISOString().split('T')[0];
//   });
//   const [postType, setPostType] = useState(() => localStorage.getItem("groupPostsPostType") || "");
//   const [socialNetwork, setSocialNetwork] = useState<string[]>(() => JSON.parse(localStorage.getItem("groupPostsSocialNetwork") || "[]"));
//   const [searchTerm, setSearchTerm] = useState(() => localStorage.getItem("groupPostsSearchTerm") || "");
//   const [sortField, setSortField] = useState<"text" | "date" | "status" | "">(() => localStorage.getItem("groupPostsSortField") as "text" | "date" | "status" | "" || "date");
//   const [sortDirection, setSortDirection] = useState<"asc" | "desc">(() => localStorage.getItem("groupPostsSortDirection") as "asc" | "desc" || "desc");
//   const [itemsPerPage] = useState(10);
//   const [currentPage, setCurrentPage] = useState(() => {
//     const savedPage = localStorage.getItem("groupPostsCurrentPage");
//     return savedPage ? parseInt(savedPage, 10) : 1;
//   });

//   // Save filter states to localStorage
//   useEffect(() => {
//     localStorage.setItem("groupPostsDateFilter", dateFilter);
//   }, [dateFilter]);

//   useEffect(() => {
//     localStorage.setItem("groupPostsStartDate", startDate);
//   }, [startDate]);

//   useEffect(() => {
//     localStorage.setItem("groupPostsEndDate", endDate);
//   }, [endDate]);

//   useEffect(() => {
//     localStorage.setItem("groupPostsPostType", postType);
//   }, [postType]);

//   useEffect(() => {
//     localStorage.setItem("groupPostsSocialNetwork", JSON.stringify(socialNetwork));
//   }, [socialNetwork]);

//   useEffect(() => {
//     localStorage.setItem("groupPostsSearchTerm", searchTerm);
//   }, [searchTerm]);

//   useEffect(() => {
//     localStorage.setItem("groupPostsSortField", sortField);
//   }, [sortField]);

//   useEffect(() => {
//     localStorage.setItem("groupPostsSortDirection", sortDirection);
//   }, [sortDirection]);

//   useEffect(() => {
//     localStorage.setItem("groupPostsCurrentPage", currentPage.toString());
//   }, [currentPage]);

//   // Validate currentPage when data changes
//   useEffect(() => {
//     const maxPage = Math.ceil(data.length / itemsPerPage);
//     if (currentPage > maxPage && maxPage > 0) {
//       setCurrentPage(1);
//       localStorage.setItem("groupPostsCurrentPage", "1");
//     }
//   }, [data, currentPage, itemsPerPage]);

//   // Fetch initial data on mount
//   useEffect(() => {
//     fetchFilteredData();
//   }, []);

//   // Apply search whenever searchTerm changes
//   useEffect(() => {
//     if (searchTerm !== "") {
//       const filtered = originalData.filter((post) => {
//         const searchLower = searchTerm.toLowerCase();
//         return (
//           (post.Posts?.[0]?.Text?.toLowerCase().includes(searchLower)) ||
//           (post.Validation?.toLowerCase().includes(searchLower)) ||
//           (post.Posts?.some((p: any) => p.SocialNetwork?.toLowerCase().includes(searchLower)))
//         );
//       });
//       setData(filtered);
//       setCurrentPage(1);
//       localStorage.setItem("groupPostsCurrentPage", "1");
//     } else {
//       setData(originalData);
//     }
//   }, [originalData, searchTerm]);

//   const fetchFilteredData = async () => {
//     setLoading(true);
//     try {
//       let filteredParams: any = {
//         validation: postType,
//         socialNetwork: socialNetwork.join(","),
//       };

//       if (dateFilter === "custom") {
//         filteredParams.startDate = startDate;
//         filteredParams.endDate = endDate;
//       } else if (dateFilter === "daily") {
//         const today = new Date();
//         today.setHours(0, 0, 0, 0);
//         filteredParams.startDate = today.toISOString();
//         today.setHours(23, 59, 59, 999);
//         filteredParams.endDate = today.toISOString();
//       } else if (dateFilter === "weekly") {
//         const today = new Date();
//         const lastWeek = new Date(today);
//         lastWeek.setDate(today.getDate() - 6);
//         lastWeek.setHours(0, 0, 0, 0);
//         today.setHours(23, 59, 59, 999);
//         filteredParams.startDate = lastWeek.toISOString();
//         filteredParams.endDate = today.toISOString();
//       } else if (dateFilter === "monthly") {
//         const today = new Date();
//         const lastMonth = new Date(today);
//         lastMonth.setDate(today.getDate() - 29);
//         lastMonth.setHours(0, 0, 0, 0);
//         today.setHours(23, 59, 59, 999);
//         filteredParams.startDate = lastMonth.toISOString();
//         filteredParams.endDate = today.toISOString();
//       }

//       filteredParams = Object.fromEntries(
//         Object.entries(filteredParams).filter(([_, v]) => v !== "" && v !== undefined)
//       );

//       const response = await apiService.getfilter<any[]>("publications", filteredParams);
//       const sortedData = [...response].sort((a, b) => {
//         const dateA = new Date(a.Posts?.[0]?.TimeStamp || 0);
//         const dateB = new Date(b.Posts?.[0]?.TimeStamp || 0);
//         return dateB.getTime() - dateA.getTime();
//       });
//       setData(sortedData);
//       setOriginalData(sortedData);
//     } catch (error) {
//       setData([]);
//       setOriginalData([]);
//       console.error("Error fetching filtered data:", error);
//     } finally {
//       setLoading(false);
//     }
//   };

//   const handleEditClick = (post: any) => {
//     setSelectedPost(post);
//     setIsModalOpen(true);
//   };

//   const handleInvalidateGroup = async (publication: any) => {
//     setLoading(true);
//     try {
//       let response;
//       if (publication.Validation?.toLowerCase() === "validated") {
//         response = await apiService.put("publications/invalidate-group", {
//           publicationID: publication.PublicationID,
//         });
//         console.log("Invalidated API Response:", response);
//       } else {
//         response = await apiService.put("publications/validate-group", {
//           publicationID: publication.PublicationID,
//         });
//         console.log("Validated API Response:", response);
//       }
//     } catch (error) {
//       console.error("Error processing group:", error);
//     } finally {
//       fetchFilteredData();
//       setLoading(false);
//     }
//   };

//   const handleFilter = () => {
//     fetchFilteredData();
//     setCurrentPage(1);
//     localStorage.setItem("groupPostsCurrentPage", "1");
//   };

//   const toggleRow = (id: number) => {
//     setExpandedRows((prev) => ({
//       ...prev,
//       [id]: !prev[id],
//     }));
//   };

//   const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
//     setSearchTerm(e.target.value);
//   };

//   const handleDateFilterChange = (value: string) => {
//     setDateFilter(value);
//     if (value !== "custom") {
//       const today = new Date();
//       let start: Date;
//       let end: Date = new Date(today);
//       end.setHours(23, 59, 59, 999);

//       if (value === "daily") {
//         start = new Date(today);
//         start.setHours(0, 0, 0, 0);
//       } else if (value === "weekly") {
//         start = new Date(today);
//         start.setDate(today.getDate() - 6);
//         start.setHours(0, 0, 0, 0);
//       } else if (value === "monthly") {
//         start = new Date(today);
//         start.setDate(today.getDate() - 29);
//         start.setHours(0, 0, 0, 0);
//       } else {
//         setStartDate("");
//         setEndDate("");
//         return;
//       }

//       setStartDate(start.toISOString().split('T')[0]);
//       setEndDate(end.toISOString().split('T')[0]);
//     }
//   };

//   const sortData = (field: "text" | "date" | "status" | "") => {
//     let direction: "asc" | "desc" = field === "date" ? "desc" : "asc";
//     if (sortField === field) {
//       direction = sortDirection === "asc" ? "desc" : "asc";
//     }

//     setSortField(field);
//     setSortDirection(direction);

//     if (field === "") {
//       setData([...originalData]);
//       return;
//     }

//     const sortedData = [...data].sort((a, b) => {
//       if (field === "text") {
//         const textA = a.Posts?.[0]?.Text?.toLowerCase() || "";
//         const textB = b.Posts?.[0]?.Text?.toLowerCase() || "";
//         return direction === "asc" ? textA.localeCompare(textB) : textB.localeCompare(textA);
//       } else if (field === "date") {
//         const dateA = new Date(a.Posts?.[0]?.TimeStamp || 0);
//         const dateB = new Date(b.Posts?.[0]?.TimeStamp || 0);
//         return direction === "asc" ? dateA.getTime() - dateB.getTime() : dateB.getTime() - dateA.getTime();
//       } else if (field === "status") {
//         const statusA = a.Validation?.toLowerCase() || "";
//         const statusB = b.Validation?.toLowerCase() || "";
//         return direction === "asc" ? statusA.localeCompare(statusB) : statusB.localeCompare(statusB);
//       }
//       return 0;
//     });

//     setData(sortedData);
//   };

//   const totalItems = data.length;
//   const startIndex = (currentPage - 1) * itemsPerPage;
//   const endIndex = Math.min(startIndex + itemsPerPage, totalItems);
//   const paginatedData = data.slice(startIndex, endIndex);

//   const handlePageChange = (newPage: number) => {
//     if (newPage >= 1 && newPage <= Math.ceil(data.length / itemsPerPage)) {
//       setCurrentPage(newPage);
//     }
//   };

//   const options = [
//     { value: "", label: "All" },
//     { value: "Validated", label: "Validated" },
//     { value: "Unvalidated", label: "Unvalidated" },
//     { value: "Partially Validated", label: "Partially Validated" },
//   ];

//   const dateFilterOptions = [
//     { value: "", label: "All" },
//     { value: "daily", label: t("group-posts.daily_posts") },
//     { value: "weekly", label: t("group-posts.last_week_posts") },
//     { value: "monthly", label: t("group-posts.last_month_posts") },
//     { value: "custom", label: t("group-posts.custom_date") },
//   ];

//   return (
//     <div className="bg-white rounded-3xl shadow xs:max-sm:mt-4 pt-4">
//       <div className={`flex flex-col xs:max-sm:px-4 sm:px-4`}>
//         <div className="w-full mb-4">
//           <div className="relative w-full">
//             <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
//               <IoIosSearch className="text-gray-400" />
//             </div>
//             <input
//               type="text"
//               placeholder={t("Search Posts") || "Search posts..."}
//               className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-3xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
//               value={searchTerm}
//               onChange={handleSearch}
//             />
//           </div>
//         </div>

//         <div className="flex flex-col sm:flex-row sm:flex-wrap gap-4 w-full">
//           <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px]">
//             <label htmlFor="date-filter" className="text-[#5A5A5A] mb-2 block">
//               {t("group-posts.date_filter")}
//             </label>
//             <Dropdown
//               value={dateFilter}
//               onChange={handleDateFilterChange}
//               options={dateFilterOptions}
//               placeholder="Select time frame"
//               className="rounded-3xl h-[47px] w-full"
//             />
//           </div>

//           {dateFilter === "custom" && (
//             <>
//               <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px]">
//                 <label htmlFor="start-date" className="text-[#5A5A5A] mb-2 block">
//                   {t("group-posts.begin")}
//                 </label>
//                 <input
//                   id="start-date"
//                   type="date"
//                   value={startDate}
//                   onChange={(e) => setStartDate(e.target.value)}
//                   className="w-full rounded-lg border border-gray-200 bg-white px-3 h-[47px] py-2 text-gray-900"
//                 />
//               </div>

//               <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px]">
//                 <label htmlFor="end-date" className="text-[#5A5A5A] mb-2 block">
//                   {t("group-posts.end")}
//                 </label>
//                 <input
//                   id="end-date"
//                   type="date"
//                   value={endDate}
//                   onChange={(e) => setEndDate(e.target.value)}
//                   className="w-full rounded-lg border border-gray-200 bg-white px-3 h-[47px] py-2 text-gray-900"
//                 />
//               </div>
//             </>
//           )}

//           <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px]">
//             <label htmlFor="type" className="text-[#5A5A5A] mb-2 block">
//               {t("group-posts.type")}
//             </label>
//             <Dropdown
//               value={postType}
//               onChange={setPostType}
//               options={options}
//               placeholder="Select post type"
//               className="rounded-3xl h-[47px] w-full"
//             />
//           </div>

//           <div className="w-full sm:flex-1 flex flex-col sm:flex-row items-end gap-4 mt-2 sm:mt-0">
//             <div className="w-full sm:flex-1">
//               <SocialNetworkChips
//                 setSocialNetwork={setSocialNetwork}
//                 socialNetwork={socialNetwork}
//                 t={t}
//               />
//             </div>
//             <div className="w-full sm:w-auto">
//               <button
//                 onClick={handleFilter}
//                 className="bg-secondary w-full sm:w-auto flex items-center justify-center gap-2 btn text-[#ffffff] py-2 px-10 text-lg h-[47px] font-light rounded-xl"
//               >
//                 <MdFilterList />
//                 {t("group-posts.button")}
//               </button>
//             </div>
//           </div>
//         </div>
//       </div>

//       <div className="mt-6 rounded-xl xs:max-sm:hidden">
//         <div className="bg-white overflow-hidden">
//           <div className="overflow-x-auto relative">
//             <table className="min-w-full border-collapse text-left text-sm">
//               <thead>
//                 <tr className="border-b border-gray-200 font-bold tracking-tight bg-[#EDF1F575] text-sm">
//                   <th className="left-col">{t("thumbnail")}</th>
//                   <th className="p-4" onClick={() => sortData("text")}>
//                     <div className="flex items-center">
//                       {t("table.columns.name")}
//                       <span className="ml-1 text-xs leading-none pl-1">
//                         {sortField === "text" ? (sortDirection === "asc" ? "↑" : "↓") : "↑↓"}
//                       </span>
//                     </div>
//                   </th>
//                   <th className="p-4">{t("table.columns.email")}</th>
//                   <th className="p-4" onClick={() => sortData("date")}>
//                     <div className="flex items-center">
//                       {t("createdTime")}
//                       <span className="ml-1 text-xs leading-none pl-1">
//                         {sortField === "date" ? (sortDirection === "asc" ? "↑" : "↓") : "↑↓"}
//                       </span>
//                     </div>
//                   </th>
//                   <th className="p-4" onClick={() => sortData("status")}>
//                     <div className="flex items-center">
//                       {t("table.columns.status")}
//                       <span className="ml-1 text-xs leading-none pl-1">
//                         {sortField === "status" ? (sortDirection === "asc" ? "↑" : "↓") : "↑↓"}
//                       </span>
//                     </div>
//                   </th>
//                   <th className="right-col">{t("table.columns.actions")}</th>
//                 </tr>
//               </thead>
//               <tbody>
//                 {loading ? (
//                   <tr>
//                     <td colSpan={5}>
//                       <Loader />
//                     </td>
//                   </tr>
//                 ) : paginatedData.length > 0 ? (
//                   paginatedData.map((item: any) => (
//                     <React.Fragment key={item.PublicationID}>
//                       <tr className="border-b border-gray-100 hover:bg-gray-50">
//                         <td className="left-col">
//                           {item.Posts?.[0]?.Thumbnail && (
//                             <img
//                               src={
//                                 item.Posts[0].Thumbnail
//                                   ? item.Posts[0].Thumbnail
//                                   : thumbnail
//                               }
//                               alt="Thumbnail"
//                               className="w-10 h-10 rounded-xl"
//                             />
//                           )}
//                         </td>
//                         <td
//                           className="p-4 text-[0.85rem] text-nowrap truncate max-w-[150px] lg:min-w-[280px]"
//                           title={item?.Posts?.[0]?.Text || "N/A"}
//                         >
//                           {item?.Posts?.[0]?.Text || "N/A"}
//                         </td>
//                         <td className="p-4 min-w-[100px]">
//                           <div className="flex items-center space-x-0.5 flex-wrap">
//                             {item.Posts?.map((post: any, i: number) => {
//                               let icon;
//                               switch (post.SocialNetwork) {
//                                 case "Facebook":
//                                   icon = (
//                                     <span>
//                                       <FaFacebookF
//                                         className="text-[1.60rem] bg-[#1877F2] p-1.5 text-[#fff] rounded-lg"
//                                         style={{
//                                           boxShadow: "0px 3px 10px 0px #1976D252",
//                                         }}
//                                       />
//                                     </span>
//                                   );
//                                   break;
//                                 case "Instagram":
//                                   icon = (
//                                     <span>
//                                       <FaInstagram
//                                         className="text-[1.60rem] p-1.5 text-white rounded-lg"
//                                         style={{
//                                           background:
//                                             "linear-gradient(45deg, #feda75, #fa7e1e, #d62976, #962fbf, #4f5bd5)",
//                                           boxShadow:
//                                             "0px 4px 4px 0px rgba(0, 0, 0, 0.2)",
//                                         }}
//                                       />
//                                     </span>
//                                   );
//                                   break;
//                                 case "LinkedIn":
//                                   icon = linkedinIcon;
//                                   break;
//                                 case "Twitter":
//                                   icon = (
//                                     <span>
//                                       <FaXTwitter
//                                         className="text-[1.60rem] bg-[#000] p-1.5 text-[#fff] rounded-lg"
//                                         style={{
//                                           boxShadow:
//                                             "0px 4px 8px rgba(0, 0, 0, 0.2)",
//                                         }}
//                                       />
//                                     </span>
//                                   );
//                                   break;
//                                 case "Youtube":
//                                   icon = (
//                                     <span>
//                                       <RiYoutubeFill
//                                         className="text-[1.60rem] bg-[#E21A20] p-1.5 text-[#fff] rounded-lg"
//                                         style={{
//                                           boxShadow: "0px 4px 4px 0px #E21A204F",
//                                         }}
//                                       />
//                                     </span>
//                                   );
//                                   break;
//                                 default:
//                                   return null;
//                               }

//                               return (
//                                 <a
//                                   key={i}
//                                   href={post.URL}
//                                   target="_blank"
//                                   rel="noopener noreferrer"
//                                 >
//                                   {icon}
//                                 </a>
//                               );
//                             })}
//                           </div>
//                         </td>

//                         <td className="p-4 text-[0.85rem]">
//                           {new Date(item.Posts[0].TimeStamp).toLocaleDateString()}
//                         </td>
//                         <td className="p-4">
//                           <span
//                             className={`px-2 py-1.5 rounded-lg text-[0.85rem] font-normal ${
//                               item.Validation === "Validated"
//                                 ? "bg-lightblue text-primary"
//                                 : item.Validation === "Unvalidated"
//                                 ? "bg-red-100 text-red-600"
//                                 : "bg-blue-100 text-blue-600"
//                             }`}
//                           >
//                             {item.Validation || "Pending"}
//                           </span>
//                         </td>
//                         <td className="right-col">
//                           <div className="flex items-center gap-1">
//                             <button
//                               className="px-2 py-2 rounded-lg bg-[#21CE9E1A] cursor-pointer text-sm text-[#21CE9E]"
//                               onClick={() => handleEditClick(item)}
//                             >
//                               <GoPencil size={16} />
//                             </button>
//                             <button
//                               className="px-2 py-2 rounded-lg text-sm cursor-pointer text-[#FB4242] bg-[#FB42421A]"
//                               onClick={() => handleInvalidateGroup(item)}
//                               disabled={item.Validation === "Validated"}
//                             >
//                               {item.Validation === "Validated" ? (
//                                 <X size={16} />
//                               ) : (
//                                 <Check size={16} />
//                               )}
//                             </button>

//                             <button
//                               className="px-2 py-2 rounded-lg text-sm bg-lightblue cursor-pointer text-primary"
//                               onClick={() => toggleRow(item.PublicationID)}
//                             >
//                               <IoIosArrowDown
//                                 className={`text-base ${
//                                   expandedRows[item.PublicationID] ? "rotate-180" : ""
//                                 } transition-transform`}
//                               />
//                             </button>
//                           </div>
//                         </td>
//                       </tr>
//                       {expandedRows[item.PublicationID] && (
//                         <tr className="border-b border-gray-200">
//                           <td colSpan={6} className="p-4 bg-gray-50">
//                             <div className="flex flex-col gap-4">
//                               {item.Posts &&
//                                 item.Posts.map((post: any, index: number) => (
//                                   <div
//                                     key={index}
//                                     className="flex flex-col sm:flex-row items-start gap-4 p-4 rounded-lg bg-white"
//                                   >
//                                     <div className="w-32 h-32 flex-shrink-0">
//                                       <img
//                                         src={
//                                           post.Thumbnail ||
//                                           "https://img.freepik.com/premium-vector/vector-flat-illustration-grayscale-avatar-user-profile-person-icon-profile-picture-business-profile-woman-suitable-social-media-profiles-icons-screensavers-as-templatex9_719432-1351.jpg?semt=ais_hybrid&w=740"
//                                         }
//                                         alt="Thumbnail"
//                                         className="w-full h-full object-cover rounded-md"
//                                       />
//                                     </div>
//                                     <div className="flex-grow">
//                                       <p className="text-lg font-semibold mb-2">
//                                         {post.Text}
//                                       </p>
//                                       <div className="flex items-center gap-2 mb-2">
//                                         {(() => {
//                                           let icon;
//                                           switch (post.SocialNetwork) {
//                                             case "Facebook":
//                                               icon = (
//                                                 <span className="h-6 w-6">
//                                                   <FaFacebookF
//                                                     className="text-2xl bg-[#1877F2] p-1.5 text-[#fff] rounded-md"
//                                                     style={{
//                                                       boxShadow:
//                                                         "0px 3px 10px 0px #1976D252",
//                                                     }}
//                                                   />
//                                                 </span>
//                                               );
//                                               break;
//                                             case "Instagram":
//                                               icon = (
//                                                 <span className="h-6 w-6">
//                                                   <FaInstagram
//                                                     className="text-2xl p-1.5 text-white rounded-md"
//                                                     style={{
//                                                       background:
//                                                         "linear-gradient(45deg, #feda75, #fa7e1e, #d62976, #962fbf, #4f5bd5)",
//                                                       boxShadow:
//                                                         "0px 4px 4px 0px rgba(0, 0, 0, 0.2)",
//                                                     }}
//                                                   />
//                                                 </span>
//                                               );
//                                               break;
//                                             case "LinkedIn":
//                                               icon = linkedinIcon;
//                                               break;
//                                             case "Twitter":
//                                               icon = (
//                                                 <span className="h-6 w-6">
//                                                   <FaXTwitter
//                                                     className="text-2xl bg-[#000] p-1.5 text-[#fff] rounded-md"
//                                                     style={{
//                                                       boxShadow:
//                                                         "0px 4px 8px rgba(0, 0, 0, 0.2)",
//                                                     }}
//                                                   />
//                                                 </span>
//                                               );
//                                               break;
//                                             case "Youtube":
//                                               icon = (
//                                                 <span className="h-6 w-6">
//                                                   <RiYoutubeFill
//                                                     className="text-2xl bg-[#E21A20] p-1.5 text-[#fff] rounded-md"
//                                                     style={{
//                                                       boxShadow:
//                                                         "0px 4px 4px 0px #E21A204F",
//                                                     }}
//                                                   />
//                                                 </span>
//                                               );
//                                               break;
//                                             default:
//                                               icon = null;
//                                           }
//                                           return icon;
//                                         })()}
//                                         <span>{post.SocialNetwork}</span>
//                                       </div>
//                                       <p className="text-gray-500 text-sm">
//                                         {new Date(post.TimeStamp).toLocaleString()}
//                                       </p>
//                                       <div className="mt-3">
//                                         <a
//                                           href={post.URL}
//                                           target="_blank"
//                                           rel="noopener noreferrer"
//                                           className="text-primary hover:underline"
//                                         >
//                                           View Original Post
//                                         </a>
//                                       </div>
//                                     </div>
//                                   </div>
//                                 ))}
//                             </div>
//                           </td>
//                         </tr>
//                       )}
//                     </React.Fragment>
//                   ))
//                 ) : (
//                   <tr>
//                     <td colSpan={5} className="text-center py-4">
//                       No data available
//                     </td>
//                   </tr>
//                 )}
//               </tbody>
//             </table>
//             {!loading && (
//               <div className="mt-4 px-8">
//                 <Pagination
//                   currentPage={currentPage}
//                   totalItems={data.length}
//                   itemsPerPage={itemsPerPage}
//                   onPageChange={handlePageChange}
//                 />
//               </div>
//             )}
//           </div>
//         </div>
//       </div>
//       {/* MOBILE UI */}
//       <div className="mt-6 rounded-xl hidden xs:max-sm:block">
//         <div className="bg-white overflow-hidden">
//           <div className="overflow-x-auto relative">
//             <table className="min-w-full border-collapse text-left text-sm">
//               <thead>
//                 <tr className="border-b border-gray-200 font-bold tracking-tight bg-[#EDF1F575] text-sm">
//                   <th className="px-8 py-5">{t("thumbnail")}</th>
//                 </tr>
//               </thead>
//               <tbody className="flex flex-col">
//                 {loading ? (
//                   <tr>
//                     <td colSpan={5}>
//                       <Loader />
//                     </td>
//                   </tr>
//                 ) : paginatedData.length > 0 ? (
//                   paginatedData.map((item: any) => (
//                     <React.Fragment key={item.PublicationID}>
//                       <tr className="border-b border-gray-100 hover:bg-gray-50">
//                         <span className="py-4 px-8 flex justify-between w-full">
//                           <td className="">
//                             {item.Posts?.[0]?.Thumbnail && (
//                               <img
//                                 src={
//                                   item.Posts[0].Thumbnail
//                                     ? item.Posts[0].Thumbnail
//                                     : thumbnail
//                                 }
//                                 alt="Thumbnail"
//                                 className="w-15 h-15 rounded-xl"
//                               />
//                             )}
//                           </td>
//                           <td className="flex flex-col items-end space-x-2">
//                             <td className="flex gap-2 flex justify-end items-end">
//                               <button
//                                 className="px-2 py-2 rounded-lg mt-3.5 text-md bg-[#21CE9E1A] cursor-pointer text-xl text-[#21CE9E]"
//                                 onClick={() => handleEditClick(item)}
//                               >
//                                 <GoPencil />
//                               </button>
//                               <button
//                                 className="px-2 py-2 rounded-lg mt-3.5 text-md cursor-pointer text-[#FB4242] bg-[#FB42421A]"
//                                 onClick={() => handleInvalidateGroup(item)}
//                                 disabled={item.Validation === "Validated"}
//                               >
//                                 {item.Validation === "Validated" ? (
//                                   <X size={20} />
//                                 ) : (
//                                   <Check size={20} />
//                                 )}
//                               </button>

//                               <button
//                                 className="px-2 py-2 rounded-lg mt-3.5 text-md bg-lightblue cursor-pointer text-primary"
//                                 onClick={() => toggleRow(item.PublicationID)}
//                               >
//                                 <IoIosArrowDown
//                                   className={`text-lg ${
//                                     expandedRows[item.PublicationID] ? "rotate-180" : ""
//                                   } transition-transform`}
//                                 />
//                               </button>
//                             </td>
//                             <td className="flex mt-3 items-end justify-end space-x-2">
//                               {item.Posts?.map((post: any, i: number) => {
//                                 let icon;
//                                 switch (post.SocialNetwork) {
//                                   case "Facebook":
//                                     icon = (
//                                       <span className="h-6 w-6">
//                                         <FaFacebookF
//                                           className="text-[1.78rem] bg-[#1877F2] p-1.5 text-[#fff] rounded-lg"
//                                           style={{
//                                             boxShadow:
//                                               "0px 3px 10px 0px #1976D252",
//                                           }}
//                                         />
//                                       </span>
//                                     );
//                                     break;
//                                   case "Instagram":
//                                     icon = (
//                                       <span className="h-6 w-6">
//                                         <FaInstagram
//                                           className="text-[1.78rem] p-1.5 text-white rounded-lg"
//                                           style={{
//                                             background:
//                                               "linear-gradient(45deg, #feda75, #fa7e1e, #d62976, #962fbf, #4f5bd5)",
//                                             boxShadow:
//                                               "0px 4px 4px 0px rgba(0, 0, 0, 0.2)",
//                                           }}
//                                         />
//                                       </span>
//                                     );
//                                     break;
//                                   case "LinkedIn":
//                                     icon = linkedinIcon;
//                                     break;
//                                   case "Twitter":
//                                     icon = (
//                                       <span className="h-6 w-6">
//                                         <FaXTwitter
//                                           className="text-[1.78rem] bg-[#000] p-1.5 text-[#fff] rounded-lg"
//                                           style={{
//                                             boxShadow:
//                                               "0px 4px 8px rgba(0, 0, 0, 0.2)",
//                                           }}
//                                         />
//                                       </span>
//                                     );
//                                     break;
//                                   case "Youtube":
//                                     icon = (
//                                       <span className="h-6 w-6">
//                                         <RiYoutubeFill
//                                           className="text-[1.78rem] bg-[#E21A20] p-1.5 text-[#fff] rounded-lg"
//                                           style={{
//                                             boxShadow:
//                                               "0px 4px 4px 0px #E21A204F",
//                                           }}
//                                         />
//                                       </span>
//                                     );
//                                     break;
//                                   default:
//                                     return null;
//                                 }

//                                 return (
//                                   <a
//                                     key={i}
//                                     href={post.URL}
//                                     target="_blank"
//                                     rel="noopener noreferrer"
//                                   >
//                                     {icon}
//                                   </a>
//                                 );
//                               })}
//                             </td>
//                           </td>
//                         </span>

//                         <span className="flex items-center justify-between">
//                           <td className="py-4 px-8 text-sm">
//                             {new Date(item.Posts[0].TimeStamp).toLocaleDateString()}
//                           </td>
//                           <td className="py-4 px-8 text-sm">
//                             <span
//                               className={`px-4 py-1.5 rounded-lg text-sm font-normal ${
//                                 item.Validation === "Validated"
//                                   ? "bg-lightblue text-primary"
//                                   : item.Validation === "Unvalidated"
//                                   ? "bg-red-100 text-red-600"
//                                   : "bg-blue-100 text-blue-600"
//                               }`}
//                             >
//                               {item.Validation || "Pending"}
//                             </span>
//                           </td>
//                         </span>
//                         <td className="py-4 px-8 max-w-[200px] text-sm text-nowrap truncate">
//                           {item?.Posts?.[0]?.Text}
//                         </td>
//                       </tr>
//                       {expandedRows[item.PublicationID] && (
//                         <tr className="border-b border-gray-200">
//                           <td colSpan={6} className="p-4 bg-gray-50">
//                             <div className="flex flex-col gap-4">
//                               {item.Posts &&
//                                 item.Posts.map((post: any, index: number) => (
//                                   <div
//                                     key={index}
//                                     className="flex flex-col sm:flex-row items-start gap-4 p-4 rounded-lg bg-white"
//                                   >
//                                     <div className="w-20 h-20 flex-shrink-0">
//                                       <img
//                                         src={
//                                           post.Thumbnail ||
//                                           "https://img.freepik.com/premium-vector/vector-flat-illustration-grayscale-avatar-user-profile-person-icon-profile-picture-business-profile-woman-suitable-social-media-profiles-icons-screensavers-as-templatex9_719432-1351.jpg?semt=ais_hybrid&w=740"
//                                         }
//                                         alt="Thumbnail"
//                                         className="w-full h-full object-cover rounded-md"
//                                       />
//                                     </div>
//                                     <div className="flex-grow">
//                                       <p className="text-lg font-semibold mb-2">
//                                         {post.Text}
//                                       </p>
//                                       <div className="flex items-center gap-2 mb-2">
//                                         {(() => {
//                                           let icon;
//                                           switch (post.SocialNetwork) {
//                                             case "Facebook":
//                                               icon = (
//                                                 <span className="h-6 w-6">
//                                                   <FaFacebookF
//                                                     className="text-2xl bg-[#1877F2] p-1.5 text-[#fff] rounded-md"
//                                                     style={{
//                                                       boxShadow:
//                                                         "0px 3px 10px 0px #1976D252",
//                                                     }}
//                                                   />
//                                                 </span>
//                                               );
//                                               break;
//                                             case "Instagram":
//                                               icon = (
//                                                 <span className="h-6 w-6">
//                                                   <FaInstagram
//                                                     className="text-2xl p-1.5 text-white rounded-md"
//                                                     style={{
//                                                       background:
//                                                         "linear-gradient(45deg, #feda75, #fa7e1e, #d62976, #962fbf, #4f5bd5)",
//                                                       boxShadow:
//                                                         "0px 4px 4px 0px rgba(0, 0, 0, 0.2)",
//                                                     }}
//                                                   />
//                                                 </span>
//                                               );
//                                               break;
//                                             case "LinkedIn":
//                                               icon = linkedinIcon;
//                                               break;
//                                             case "Twitter":
//                                               icon = (
//                                                 <span className="h-6 w-6">
//                                                   <FaXTwitter
//                                                     className="text-2xl bg-[#000] p-1.5 text-[#fff] rounded-md"
//                                                     style={{
//                                                       boxShadow:
//                                                         "0px 4px 8px rgba(0, 0, 0, 0.2)",
//                                                     }}
//                                                   />
//                                                 </span>
//                                               );
//                                               break;
//                                             case "Youtube":
//                                               icon = (
//                                                 <span className="h-6 w-6">
//                                                   <RiYoutubeFill
//                                                     className="text-2xl bg-[#E21A20] p-1.5 text-[#fff] rounded-md"
//                                                     style={{
//                                                       boxShadow:
//                                                         "0px 4px 4px 0px #E21A204F",
//                                                     }}
//                                                   />
//                                                 </span>
//                                               );
//                                               break;
//                                             default:
//                                               icon = null;
//                                           }
//                                           return icon;
//                                         })()}
//                                         <span>{post.SocialNetwork}</span>
//                                       </div>
//                                       <p className="text-gray-500 text-sm">
//                                         {new Date(post.TimeStamp).toLocaleString()}
//                                       </p>
//                                       <div className="mt-3">
//                                         <a
//                                           href={post.URL}
//                                           target="_blank"
//                                           rel="noopener noreferrer"
//                                           className="text-primary hover:underline"
//                                         >
//                                           View Original Post
//                                         </a>
//                                       </div>
//                                     </div>
//                                   </div>
//                                 ))}
//                             </div>
//                           </td>
//                         </tr>
//                       )}
//                     </React.Fragment>
//                   ))
//                 ) : (
//                   <tr>
//                     <td colSpan={5} className="text-center py-4">
//                       No data available
//                     </td>
//                   </tr>
//                 )}
//               </tbody>
//             </table>
//             {!loading && (
//               <div className="mt-4 px-8">
//                 <Pagination
//                   currentPage={currentPage}
//                   totalItems={data.length}
//                   itemsPerPage={itemsPerPage}
//                   onPageChange={handlePageChange}
//                 />
//               </div>
//             )}
//           </div>
//         </div>
//       </div>
//       <Editmodal
//         isOpen={isModalOpen}
//         onClose={() => setIsModalOpen(false)}
//         onOpenAddModal={() => {
//           setIsModalOpen(false);
//           setIsAddModalOpen(true);
//         }}
//         selectedPost={selectedPost}
//         refreshData={() => fetchFilteredData()}
//       />

//       <AddModal
//         isOpen={isAddModalOpen}
//         onClose={() => setIsAddModalOpen(false)}
//         selectedPost={selectedPost}
//         data={data}
//         refreshData={() => fetchFilteredData()}
//       />
//     </div>
//   );
// }

// export default GroupPosts;

import { Check, X } from "lucide-react";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { FaFacebookF, FaInstagram, FaXTwitter } from "react-icons/fa6";
import { GoPencil } from "react-icons/go";
import { IoIosArrowDown, IoIosSearch } from "react-icons/io";
import { MdFilterList } from "react-icons/md";
import { RiYoutubeFill } from "react-icons/ri";
import linkedinIcon from "../assets/Linkedin.svg";
import thumbnail from "../assets/thumbnail.svg";
import Dropdown from "../components/Dropdown";
import Pagination from "../components/Pagination";
import SocialNetworkChips from "../components/SocialNetworkChips";
import apiService from "../services/apiService";
import AddModal from "./addModal";
import Editmodal from "./editmodal";
import Loader from "./loader";

interface Post {
  PublicationID: number;
  Posts: {
    PostID: number;
    Text: string;
    SocialNetwork: string;
    TimeStamp: string;
    LocalTimeStamp?: string;
    Thumbnail: string;
    URL: string;
    Validation?: string;
  }[];
  Validation: string;
}

function GroupPosts() {
  const { t } = useTranslation();
  const [expandedRows, setExpandedRows] = useState<{ [key: number]: boolean }>({});
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  // const [selectedPost, setSelectedPost] = useState<Post | null>(null);

 const [selectedPost, setSelectedPost] = useState<any>(null); 
  const [data, setData] = useState<Post[]>([]);
  const [originalData, setOriginalData] = useState<Post[]>([]);
  const [loading, setLoading] = useState(false);
  
  // Filter states with localStorage persistence
  const [dateFilter, setDateFilter] = useState(() => localStorage.getItem("groupPostsDateFilter") || "monthly");
  const [startDate, setStartDate] = useState(() => {
    const savedDate = localStorage.getItem("groupPostsStartDate");
    if (savedDate) return savedDate;
    
    const today = new Date();
    const lastMonth = new Date(today);
    lastMonth.setDate(today.getDate() - 29);
    lastMonth.setHours(0, 0, 0, 0);
    return lastMonth.toISOString().split('T')[0];
  });
  
  const [endDate, setEndDate] = useState(() => {
    const savedDate = localStorage.getItem("groupPostsEndDate");
    if (savedDate) return savedDate;
    
    const today = new Date();
    today.setHours(23, 59, 59, 999);
    return today.toISOString().split('T')[0];
  });
  
  const [postType, setPostType] = useState(() => localStorage.getItem("groupPostsPostType") || "");
  const [socialNetwork, setSocialNetwork] = useState<string[]>(() => {
    const saved = localStorage.getItem("groupPostsSocialNetwork");
    return saved ? JSON.parse(saved) : [];
  });
  
  const [searchTerm, setSearchTerm] = useState(() => localStorage.getItem("groupPostsSearchTerm") || "");
  const [sortField, setSortField] = useState<"text" | "date" | "status" | "">(() => {
    const saved = localStorage.getItem("groupPostsSortField");
    return (saved as "text" | "date" | "status" | "") || "date";
  });
  
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">(() => {
    const saved = localStorage.getItem("groupPostsSortDirection");
    return (saved as "asc" | "desc") || "desc";
  });
  
  const [itemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(() => {
    const savedPage = localStorage.getItem("groupPostsCurrentPage");
    return savedPage ? parseInt(savedPage, 10) : 1;
  });

  // Save states to localStorage
  useEffect(() => {
    localStorage.setItem("groupPostsDateFilter", dateFilter);
    localStorage.setItem("groupPostsStartDate", startDate);
    localStorage.setItem("groupPostsEndDate", endDate);
    localStorage.setItem("groupPostsPostType", postType);
    localStorage.setItem("groupPostsSocialNetwork", JSON.stringify(socialNetwork));
    localStorage.setItem("groupPostsSearchTerm", searchTerm);
    localStorage.setItem("groupPostsSortField", sortField);
    localStorage.setItem("groupPostsSortDirection", sortDirection);
    localStorage.setItem("groupPostsCurrentPage", currentPage.toString());
  }, [
    dateFilter, startDate, endDate, postType, 
    socialNetwork, searchTerm, sortField, 
    sortDirection, currentPage
  ]);

  // Validate currentPage when data changes
  useEffect(() => {
    const maxPage = Math.ceil(data.length / itemsPerPage);
    if (currentPage > maxPage && maxPage > 0) {
      setCurrentPage(1);
    }
  }, [data, currentPage, itemsPerPage]);

  // Fetch initial data
  useEffect(() => {
    fetchFilteredData();
  }, []);

  // Apply search filter
  useEffect(() => {
    if (searchTerm) {
      const filtered = originalData.filter(post => 
        post.Posts?.[0]?.Text?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.Validation?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.Posts?.some(p => p.SocialNetwork?.toLowerCase().includes(searchTerm.toLowerCase()))
      );
      setData(filtered);
      setCurrentPage(1);
    } else {
      setData(originalData);
    }
  }, [searchTerm, originalData]);

  const fetchFilteredData = async () => {
    setLoading(true);
    try {
      const params: Record<string, string> = {
        validation: postType,
        socialNetwork: socialNetwork.join(","),
      };

      // Set date range based on filter
      if (dateFilter === "custom") {
        params.startDate = startDate;
        params.endDate = endDate;
      } else if (dateFilter === "daily") {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        params.startDate = today.toISOString();
        today.setHours(23, 59, 59, 999);
        params.endDate = today.toISOString();
      } else if (dateFilter === "weekly") {
        const today = new Date();
        const lastWeek = new Date(today);
        lastWeek.setDate(today.getDate() - 6);
        lastWeek.setHours(0, 0, 0, 0);
        today.setHours(23, 59, 59, 999);
        params.startDate = lastWeek.toISOString();
        params.endDate = today.toISOString();
      } else if (dateFilter === "monthly") {
        const today = new Date();
        const lastMonth = new Date(today);
        lastMonth.setDate(today.getDate() - 29);
        lastMonth.setHours(0, 0, 0, 0);
        today.setHours(23, 59, 59, 999);
        params.startDate = lastMonth.toISOString();
        params.endDate = today.toISOString();
      }

      // Remove empty params
      const filteredParams = Object.fromEntries(
        Object.entries(params).filter(([_, v]) => v && v !== "")
      );

      const response = await apiService.getfilter<Post[]>("publications", filteredParams);
      const sortedData = [...response].sort((a, b) => {
        const dateA = new Date(a.Posts?.[0]?.TimeStamp || 0);
        const dateB = new Date(b.Posts?.[0]?.TimeStamp || 0);
        return dateB.getTime() - dateA.getTime();
      });
      
      setData(sortedData);
      setOriginalData(sortedData);
    } catch (error) {
      console.error("Error fetching data:", error);
      setData([]);
      setOriginalData([]);
    } finally {
      setLoading(false);
    }
  };

  const handleEditClick = (post: Post) => {
    setSelectedPost(post);
    setIsModalOpen(true);
  };

  const handleInvalidateGroup = async (publication: Post) => {
    setLoading(true);
    try {
      if (publication.Validation?.toLowerCase() === "validated") {
        await apiService.put("publications/invalidate-group", {
          publicationID: publication.PublicationID,
        });
      } else {
        await apiService.put("publications/validate-group", {
          publicationID: publication.PublicationID,
        });
      }
      fetchFilteredData();
    } catch (error) {
      console.error("Error updating validation:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleFilter = () => {
    fetchFilteredData();
    setCurrentPage(1);
  };

  const toggleRow = (id: number) => {
    setExpandedRows(prev => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleDateFilterChange = (value: string) => {
    setDateFilter(value);
    
    if (value !== "custom") {
      const today = new Date();
      let start = new Date(today);
      const end = new Date(today);
      end.setHours(23, 59, 59, 999);

      if (value === "daily") {
        start.setHours(0, 0, 0, 0);
      } else if (value === "weekly") {
        start.setDate(today.getDate() - 6);
        start.setHours(0, 0, 0, 0);
      } else if (value === "monthly") {
        start.setDate(today.getDate() - 29);
        start.setHours(0, 0, 0, 0);
      }

      setStartDate(start.toISOString().split('T')[0]);
      setEndDate(end.toISOString().split('T')[0]);
    }
  };

  const sortData = (field: "text" | "date" | "status" | "") => {
    let direction: "asc" | "desc" = "asc";
    
    if (sortField === field) {
      direction = sortDirection === "asc" ? "desc" : "asc";
    } else if (field === "date") {
      direction = "desc";
    }

    setSortField(field);
    setSortDirection(direction);

    if (!field) {
      setData([...originalData]);
      return;
    }

    const sortedData = [...data].sort((a, b) => {
      if (field === "text") {
        const textA = a.Posts?.[0]?.Text?.toLowerCase() || "";
        const textB = b.Posts?.[0]?.Text?.toLowerCase() || "";
        return direction === "asc" ? textA.localeCompare(textB) : textB.localeCompare(textA);
      } else if (field === "date") {
        const dateA = new Date(a.Posts?.[0]?.TimeStamp || 0);
        const dateB = new Date(b.Posts?.[0]?.TimeStamp || 0);
        return direction === "asc" ? dateA.getTime() - dateB.getTime() : dateB.getTime() - dateA.getTime();
      } else if (field === "status") {
        const statusA = a.Validation?.toLowerCase() || "";
        const statusB = b.Validation?.toLowerCase() || "";
        return direction === "asc" ? statusA.localeCompare(statusB) : statusB.localeCompare(statusA);
      }
      return 0;
    });

    setData(sortedData);
  };

  // Pagination
  const totalItems = data.length;
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, totalItems);
  const paginatedData = data.slice(startIndex, endIndex);

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= Math.ceil(data.length / itemsPerPage)) {
      setCurrentPage(newPage);
    }
  };

  // Options for dropdowns
  const statusOptions = [
    { value: "", label: t("groupPosts.status.all") || "All" },
    { value: "Validated", label: t("groupPosts.status.validated") },
    { value: "Unvalidated", label: t("groupPosts.status.unvalidated") },
    { value: "Partially Validated", label: t("groupPosts.status.partially_validated") },
  ];

  const dateFilterOptions = [
    { value: "", label: t("groupPosts.filters.all") || "All" },
    { value: "daily", label: t("groupPosts.filters.daily_posts") },
    { value: "weekly", label: t("groupPosts.filters.last_week_posts") },
    { value: "monthly", label: t("groupPosts.filters.last_month_posts") },
    { value: "custom", label: t("groupPosts.filters.custom_date") },
  ];

  const getSocialNetworkIcon = (network: string) => {
    switch (network) {
      case "Facebook":
        return (
          <FaFacebookF
            className="text-[1.60rem] bg-[#1877F2] p-1.5 text-[#fff] rounded-lg"
            style={{ boxShadow: "0px 3px 10px 0px #1976D252" }}
          />
        );
      case "Instagram":
        return (
          <FaInstagram
            className="text-[1.60rem] p-1.5 text-white rounded-lg"
            style={{
              background: "linear-gradient(45deg, #feda75, #fa7e1e, #d62976, #962fbf, #4f5bd5)",
              boxShadow: "0px 4px 4px 0px rgba(0, 0, 0, 0.2)",
            }}
          />
        );
      case "LinkedIn":
        return <img src={linkedinIcon} alt="LinkedIn" className="w-8 h-8" />;
      case "Twitter":
        return (
          <FaXTwitter
            className="text-[1.60rem] bg-[#000] p-1.5 text-[#fff] rounded-lg"
            style={{ boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.2)" }}
          />
        );
      case "Youtube":
        return (
          <RiYoutubeFill
            className="text-[1.60rem] bg-[#E21A20] p-1.5 text-[#fff] rounded-lg"
            style={{ boxShadow: "0px 4px 4px 0px #E21A204F" }}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="bg-white rounded-3xl shadow xs:max-sm:mt-4 pt-4">
      {/* Filters Section */}
      <div className="flex flex-col xs:max-sm:px-4 sm:px-4">
        {/* Search */}
        <div className="w-full mb-4">
          <div className="relative w-full">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <IoIosSearch className="text-gray-400" />
            </div>
            <input
              type="text"
              placeholder={t("groupPosts.search")}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-3xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              value={searchTerm}
              onChange={handleSearch}
            />
          </div>
        </div>

        {/* Filter Controls */}
        <div className="flex flex-col sm:flex-row sm:flex-wrap gap-4 w-full">
          {/* Date Filter */}
          <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px]">
            <label htmlFor="date-filter" className="text-[#5A5A5A] mb-2 block">
              {t("groupPosts.filters.date_filter")}
            </label>
            <Dropdown
              value={dateFilter}
              onChange={handleDateFilterChange}
              options={dateFilterOptions}
              placeholder={t("groupPosts.filters.date_filter")}
              className="rounded-3xl h-[47px] w-full"
            />
          </div>

          {/* Custom Date Range */}
          {dateFilter === "custom" && (
            <>
              <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px]">
                <label htmlFor="start-date" className="text-[#5A5A5A] mb-2 block">
                  {t("groupPosts.filters.begin")}
                </label>
                <input
                  id="start-date"
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="w-full rounded-lg border border-gray-200 bg-white px-3 h-[47px] py-2 text-gray-900"
                />
              </div>

              <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px]">
                <label htmlFor="end-date" className="text-[#5A5A5A] mb-2 block">
                  {t("groupPosts.filters.end")}
                </label>
                <input
                  id="end-date"
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="w-full rounded-lg border border-gray-200 bg-white px-3 h-[47px] py-2 text-gray-900"
                />
              </div>
            </>
          )}

          {/* Status Filter */}
          <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px]">
            <label htmlFor="type" className="text-[#5A5A5A] mb-2 block">
              {t("groupPosts.filters.type")}
            </label>
            <Dropdown
              value={postType}
              onChange={setPostType}
              options={statusOptions}
              placeholder={t("groupPosts.filters.type")}
              className="rounded-3xl h-[47px] w-full"
            />
          </div>

          {/* Social Network Chips and Filter Button */}
          <div className="w-full sm:flex-1 flex flex-col sm:flex-row items-end gap-4 mt-2 sm:mt-0">
            <div className="w-full sm:flex-1">
              <SocialNetworkChips
                setSocialNetwork={setSocialNetwork}
                socialNetwork={socialNetwork}
                t={t}
              />
            </div>
            <div className="w-full sm:w-auto">
              <button
                onClick={handleFilter}
                className="bg-secondary w-full sm:w-auto flex items-center justify-center gap-2 btn text-[#ffffff] py-2 px-10 text-lg h-[47px] font-light rounded-xl"
              >
                <MdFilterList />
                {t("groupPosts.filters.button")}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Desktop Table */}
      <div className="mt-6 rounded-xl xs:max-sm:hidden">
        <div className="bg-white overflow-hidden">
          <div className="overflow-x-auto relative">
            <table className="min-w-full border-collapse text-left text-sm">
              <thead>
                <tr className="border-b border-gray-200 font-bold tracking-tight bg-[#EDF1F575] text-sm">
                  <th className="left-col">{t("groupPosts.table.thumbnail")}</th>
                  <th className="p-4" onClick={() => sortData("text")}>
                    <div className="flex items-center">
                      {t("groupPosts.table.columns.name")}
                      <span className="ml-1 text-xs leading-none pl-1">
                        {sortField === "text" ? (sortDirection === "asc" ? "↑" : "↓") : "↑↓"}
                      </span>
                    </div>
                  </th>
                  <th className="p-4">{t("groupPosts.table.columns.email")}</th>
                  <th className="p-4" onClick={() => sortData("date")}>
                    <div className="flex items-center">
                      {t("groupPosts.table.createdTime")}
                      <span className="ml-1 text-xs leading-none pl-1">
                        {sortField === "date" ? (sortDirection === "asc" ? "↑" : "↓") : "↑↓"}
                      </span>
                    </div>
                  </th>
                  <th className="p-4" onClick={() => sortData("status")}>
                    <div className="flex items-center">
                      {t("groupPosts.table.columns.status")}
                      <span className="ml-1 text-xs leading-none pl-1">
                        {sortField === "status" ? (sortDirection === "asc" ? "↑" : "↓") : "↑↓"}
                      </span>
                    </div>
                  </th>
                  <th className="right-col">{t("groupPosts.table.columns.actions")}</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan={6} className="py-8">
                      <Loader />
                    </td>
                  </tr>
                ) : paginatedData.length > 0 ? (
                  paginatedData.map((item) => (
                    <React.Fragment key={item.PublicationID}>
                      <tr className="border-b border-gray-100 hover:bg-gray-50">
                        {/* Thumbnail */}
                        <td className="left-col">
                          <img
                            src={item.Posts?.[0]?.Thumbnail || thumbnail}
                            alt="Thumbnail"
                            className="w-10 h-10 rounded-xl"
                          />
                        </td>
                        
                        {/* Post Text */}
                        <td
                          className="p-4 text-[0.85rem] text-nowrap truncate max-w-[150px] lg:min-w-[280px]"
                          title={item.Posts?.[0]?.Text || "N/A"}
                        >
                          {item.Posts?.[0]?.Text || "N/A"}
                        </td>
                        
                        {/* Social Networks */}
                        <td className="p-4 min-w-[100px]">
                          <div className="flex items-center space-x-0.5 flex-wrap">
                            {item.Posts?.map((post, i) => (
                              <a
                                key={i}
                                href={post.URL}
                                target="_blank"
                                rel="noopener noreferrer"
                              >
                                {getSocialNetworkIcon(post.SocialNetwork)}
                              </a>
                            ))}
                          </div>
                        </td>
                        
                        {/* Date */}
                        <td className="p-4 text-[0.85rem]">
                          {new Date(item.Posts[0].TimeStamp).toLocaleDateString()}
                        </td>
                        
                        {/* Status */}
                        <td className="p-4">
                          <span
                            className={`px-2 py-1.5 rounded-lg text-[0.85rem] font-normal ${
                              item.Validation === "Validated"
                                ? "bg-lightblue text-primary"
                                : item.Validation === "Unvalidated"
                                ? "bg-red-100 text-red-600"
                                : "bg-blue-100 text-blue-600"
                            }`}
                          >
                            {item.Validation || "Pending"}
                          </span>
                        </td>
                        
                        {/* Actions */}
                        <td className="right-col">
                          <div className="flex items-center gap-1">
                            <button
                              className="px-2 py-2 rounded-lg bg-[#21CE9E1A] cursor-pointer text-sm text-[#21CE9E]"
                              onClick={() => handleEditClick(item)}
                            >
                              <GoPencil size={16} />
                            </button>
                            
                            <button
                              className="px-2 py-2 rounded-lg text-sm cursor-pointer text-[#FB4242] bg-[#FB42421A]"
                              onClick={() => handleInvalidateGroup(item)}
                              disabled={item.Validation === "Validated"}
                            >
                              {item.Validation === "Validated" ? (
                                <X size={16} />
                              ) : (
                                <Check size={16} />
                              )}
                            </button>

                            <button
                              className="px-2 py-2 rounded-lg text-sm bg-lightblue cursor-pointer text-primary"
                              onClick={() => toggleRow(item.PublicationID)}
                            >
                              <IoIosArrowDown
                                className={`text-base ${
                                  expandedRows[item.PublicationID] ? "rotate-180" : ""
                                } transition-transform`}
                              />
                            </button>
                          </div>
                        </td>
                      </tr>
                      
                      {/* Expanded Row */}
                      {expandedRows[item.PublicationID] && (
                        <tr className="border-b border-gray-200">
                          <td colSpan={6} className="p-4 bg-gray-50">
                            <div className="flex flex-col gap-4">
                              {item.Posts?.map((post, index) => (
                                <div
                                  key={index}
                                  className="flex flex-col sm:flex-row items-start gap-4 p-4 rounded-lg bg-white"
                                >
                                  <div className="w-32 h-32 flex-shrink-0">
                                    <img
                                      src={post.Thumbnail || thumbnail}
                                      alt="Thumbnail"
                                      className="w-full h-full object-cover rounded-md"
                                    />
                                  </div>
                                  <div className="flex-grow">
                                    <p className="text-lg font-semibold mb-2">
                                      {post.Text}
                                    </p>
                                    <div className="flex items-center gap-2 mb-2">
                                      {getSocialNetworkIcon(post.SocialNetwork)}
                                      <span>{post.SocialNetwork}</span>
                                    </div>
                                    <p className="text-gray-500 text-sm">
                                      {new Date(post.TimeStamp).toLocaleString()}
                                    </p>
                                    <div className="mt-3">
                                      <a
                                        href={post.URL}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-primary hover:underline"
                                      >
                                        {t("groupPosts.table.view_original")}
                                      </a>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </td>
                        </tr>
                      )}
                    </React.Fragment>
                  ))
                ) : (
                  <tr>
                    <td colSpan={6} className="text-center py-4">
                      {t("groupPosts.table.no_data")}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
            
            {/* Pagination */}
            {!loading && (
              <div className="mt-4 px-8">
                <Pagination
                  currentPage={currentPage}
                  totalItems={data.length}
                  itemsPerPage={itemsPerPage}
                  onPageChange={handlePageChange}
                />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Mobile Table */}
      <div className="mt-6 rounded-xl hidden xs:max-sm:block">
        <div className="bg-white overflow-hidden">
          <div className="overflow-x-auto relative">
            <table className="min-w-full border-collapse text-left text-sm">
              <thead>
                <tr className="border-b border-gray-200 font-bold tracking-tight bg-[#EDF1F575] text-sm">
                  <th className="px-8 py-5">{t("groupPosts.table.thumbnail")}</th>
                </tr>
              </thead>
              <tbody className="flex flex-col">
                {loading ? (
                  <tr>
                    <td colSpan={5}>
                      <Loader />
                    </td>
                  </tr>
                ) : paginatedData.length > 0 ? (
                  paginatedData.map((item) => (
                    <React.Fragment key={item.PublicationID}>
                      <tr className="border-b border-gray-100 hover:bg-gray-50">
                        <span className="py-4 px-8 flex justify-between w-full">
                          <td>
                            <img
                              src={item.Posts?.[0]?.Thumbnail || thumbnail}
                              alt="Thumbnail"
                              className="w-15 h-15 rounded-xl"
                            />
                          </td>
                          <td className="flex flex-col items-end space-x-2">
                            <td className="flex gap-2 flex justify-end items-end">
                              <button
                                className="px-2 py-2 rounded-lg mt-3.5 text-md bg-[#21CE9E1A] cursor-pointer text-xl text-[#21CE9E]"
                                onClick={() => handleEditClick(item)}
                              >
                                <GoPencil />
                              </button>
                              <button
                                className="px-2 py-2 rounded-lg mt-3.5 text-md cursor-pointer text-[#FB4242] bg-[#FB42421A]"
                                onClick={() => handleInvalidateGroup(item)}
                                disabled={item.Validation === "Validated"}
                              >
                                {item.Validation === "Validated" ? (
                                  <X size={20} />
                                ) : (
                                  <Check size={20} />
                                )}
                              </button>
                              <button
                                className="px-2 py-2 rounded-lg mt-3.5 text-md bg-lightblue cursor-pointer text-primary"
                                onClick={() => toggleRow(item.PublicationID)}
                              >
                                <IoIosArrowDown
                                  className={`text-lg ${
                                    expandedRows[item.PublicationID] ? "rotate-180" : ""
                                  } transition-transform`}
                                />
                              </button>
                            </td>
                            <td className="flex mt-3 items-end justify-end space-x-2">
                              {item.Posts?.map((post, i) => (
                                <a
                                  key={i}
                                  href={post.URL}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                >
                                  {getSocialNetworkIcon(post.SocialNetwork)}
                                </a>
                              ))}
                            </td>
                          </td>
                        </span>

                        <span className="flex items-center justify-between">
                          <td className="py-4 px-8 text-sm">
                            {new Date(item.Posts[0].TimeStamp).toLocaleDateString()}
                          </td>
                          <td className="py-4 px-8 text-sm">
                            <span
                              className={`px-4 py-1.5 rounded-lg text-sm font-normal ${
                                item.Validation === "Validated"
                                  ? "bg-lightblue text-primary"
                                  : item.Validation === "Unvalidated"
                                  ? "bg-red-100 text-red-600"
                                  : "bg-blue-100 text-blue-600"
                              }`}
                            >
                              {item.Validation || "Pending"}
                            </span>
                          </td>
                        </span>
                        <td className="py-4 px-8 max-w-[200px] text-sm text-nowrap truncate">
                          {item.Posts?.[0]?.Text}
                        </td>
                      </tr>
                      
                      {/* Expanded Row */}
                      {expandedRows[item.PublicationID] && (
                        <tr className="border-b border-gray-200">
                          <td colSpan={6} className="p-4 bg-gray-50">
                            <div className="flex flex-col gap-4">
                              {item.Posts?.map((post, index) => (
                                <div
                                  key={index}
                                  className="flex flex-col sm:flex-row items-start gap-4 p-4 rounded-lg bg-white"
                                >
                                  <div className="w-20 h-20 flex-shrink-0">
                                    <img
                                      src={post.Thumbnail || thumbnail}
                                      alt="Thumbnail"
                                      className="w-full h-full object-cover rounded-md"
                                    />
                                  </div>
                                  <div className="flex-grow">
                                    <p className="text-lg font-semibold mb-2">
                                      {post.Text}
                                    </p>
                                    <div className="flex items-center gap-2 mb-2">
                                      {getSocialNetworkIcon(post.SocialNetwork)}
                                      <span>{post.SocialNetwork}</span>
                                    </div>
                                    <p className="text-gray-500 text-sm">
                                      {new Date(post.TimeStamp).toLocaleString()}
                                    </p>
                                    <div className="mt-3">
                                      <a
                                        href={post.URL}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-primary hover:underline"
                                      >
                                        {t("groupPosts.table.view_original")}
                                      </a>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </td>
                        </tr>
                      )}
                    </React.Fragment>
                  ))
                ) : (
                  <tr>
                    <td colSpan={5} className="text-center py-4">
                      {t("groupPosts.table.no_data")}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
            
            {/* Pagination */}
            {!loading && (
              <div className="mt-4 px-8">
                <Pagination
                  currentPage={currentPage}
                  totalItems={data.length}
                  itemsPerPage={itemsPerPage}
                  onPageChange={handlePageChange}
                />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Modals */}
      <Editmodal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onOpenAddModal={() => {
          setIsModalOpen(false);
          setIsAddModalOpen(true);
        }}
        selectedPost={selectedPost}
        refreshData={fetchFilteredData}
      />

      <AddModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        selectedPost={selectedPost}
        data={data}
        refreshData={fetchFilteredData}
      />
    </div>
  );
}

export default GroupPosts;