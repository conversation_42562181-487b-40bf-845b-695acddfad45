import { <PERSON>, <PERSON><PERSON>lus, CircleX, Trash2 } from "lucide-react";
// import { Check, CirclePlus, CircleX, Trash2, X } from "lucide-react";
import facebookIcon from "../assets/Facebook.svg";
import instagramIcon from "../assets/Instagram.svg";
import linkedinIcon from "../assets/Linkedin.svg";
import TwitterIcon from "../assets/x.svg";
import YoutubeIcon from "../assets/youtube.svg";
import apiService from "../services/apiService";

interface Post {
  PostID: string;
  Thumbnail: string;
  Text: string;
  SocialNetwork: string;
  TimeStamp: string;
  LocalTimeStamp: string;
  Validation: boolean;
}

interface SelectedPost {
  Posts: Post[];
  PublicationID: string;
}

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  onOpenAddModal: () => void;
  selectedPost: SelectedPost | null;
  refreshData: () => void;
}

function EditModal({
  isOpen,
  onClose,
  onOpenAddModal,
  selectedPost,
  refreshData,
}: ModalProps) {
  if (!isOpen || !selectedPost) return null;

  const handleDelete = async (postID: string) => {
    try {
      const response = await apiService.put(`/publications/remove-post`, {
        postID: postID,
      });
      console.log(response);
    } catch (error) {
      console.error("Error deleting post:", error);
      alert("Failed to delete post.");
    } finally {
      // onClose();
      refreshData();
    }
  };

  const handleInvalidatePost = async (Post: any) => {
    try {
      let response;
      if (Post.Validation === true) {
        response = await apiService.put("publications/invalidate-post", {
          postID: Post.PostID,
        });
        console.log("Invalidated API Response:", response);
      } else {
        response = await apiService.put("publications/validate-post", {
          postID: Post.PostID,
        });
        console.log("Validated API Response:", response);
      }
    } catch (error) {
      console.error("Error processing group:", error);
    } finally {
      // onClose();
      refreshData();
    }
  };

  return (
    <div
      className="fixed inset-0 flex items-center justify-center bg-[#00000052] bg-opacity-50 z-1000"
      onClick={onClose}>
      <div className="bg-white pt-4 pb-10 px-6 rounded-3xl shadow-lg max-w-[60%] xs:max-sm:w-[90%] xs:max-sm:max-w-[90%] w-full">
        <div className="flex justify-between items-center">
          <h2 className="text-[21px] font-semibold tracking-tight noto-sans">
            Edit Data
          </h2>
          <CircleX size={28} onClick={onClose} className="cursor-pointer" />
        </div>
        <div className="flex w-full items-end gap-3 justify-end mt-4">
          {/* <span>
            <p className="text-gray-600 text-xl font-light mt-4">
              Publication ID:
            </p>
            <h2 className="text-xl font-light">{selectedPost.PublicationID}</h2>
          </span> */}
          <button
            className="bg-primary flex items-center py-3 px-4 justify-center gap-2 btn text-white text-md font-extralight rounded-xl"
            onClick={onOpenAddModal}
          >
            <CirclePlus className="font-lighter" size={20} /> Add Post
          </button>
        </div>

        {/* <div className="overflow-x-auto scrollbar-hidden mt-6 rounded-xl">
          <div className="min-w-[768px]">
            <table className="w-full border-collapse text-left p-4 border border-gray-200"> */}
        <div className="mt-6 rounded-xl max-h-[60vh]">
          <div className="min-w-[768px] overflow-x-auto">
            <table className="w-full border-collapse text-left p-4 border border-gray-200">
              <thead>
                <tr className="border-b border-gray-200 bg-lightblue font-normal text-md">
                  <th className="p-3 font-extralight">Thumbnail</th>
                  <th className="p-3 font-extralight">Text</th>
                  <th className="p-3 font-extralight">Social Network</th>
                  <th className="p-3 font-extralight">Local Timestamp</th>
                  <th className="p-3 font-extralight">Validation</th>
                  <th className="p-3 font-extralight">Actions</th>
                </tr>
              </thead>
              <tbody>
                {selectedPost.Posts.map((postItem, index) => (
                  <tr
                    key={index}
                    className="border-b border-gray-100 hover:bg-gray-50"
                  >
                    <td className="p-3">
                      <img
                        src={postItem.Thumbnail}
                        alt="Thumbnail"
                        className="w-16 h-16 rounded-xl"
                      />
                    </td>
                    <td className="p-3 max-w-xs">
                      <p className="truncate">{postItem.Text}</p>
                    </td>
                    <td className="p-3">
                      {(() => {
                        let icon;
                        switch (postItem.SocialNetwork) {
                          case "Facebook":
                            icon = facebookIcon;
                            break;
                          case "Instagram":
                            icon = instagramIcon;
                            break;
                          case "LinkedIn":
                            icon = linkedinIcon;
                            break;
                          case "Twitter":
                            icon = TwitterIcon;
                            break;
                          case "Youtube":
                            icon = YoutubeIcon;
                            break;
                          default:
                            return null;
                        }
                        return icon ? (
                          <img
                            src={icon}
                            alt={postItem.SocialNetwork}
                            className="h-[60px] w-[60px]"
                          />
                        ) : null;
                      })()}
                    </td>
                    <td className="p-3">
                      <span
                        className={`px-4 py-1 rounded-sm text-sm ${postItem.Validation
                          ? "bg-[#E9FAF5] text-[#21CE9E]"
                          : "bg-red-100 text-red-600"
                          }`}
                      >
                        {postItem.Validation ? "Validated" : "Unvalidated"}
                      </span>
                    </td>
                    <td className="p-3 whitespace-nowrap">
                      {new Date(postItem.LocalTimeStamp).toLocaleDateString()}
                    </td>

                    <td className="p-3">
                      <div className="flex gap-2 whitespace-nowrap">
                        {/* <button
                          className="p-2 rounded-md btn"
                          onClick={() => handleInvalidatePost(postItem)}
                          disabled={postItem.Validation === true}
                        >
                          {postItem.Validation === true ? (
                            <X size={20} />
                          ) : (
                            <Check size={20} />
                          )}
                        </button> */}
                        <button
                          className="p-2 rounded-md btn"
                          onClick={() => handleInvalidatePost(postItem)}
                          disabled={postItem.Validation === true}
                        >
                          {postItem.Validation === true ? null : <Check size={20} />}
                        </button>

                        <button
                          className="bg-[#FFECEC] flex items-center justify-center btn py-3 px-3 rounded-lg"
                          onClick={() => handleDelete(postItem.PostID)}
                        >
                          <Trash2 size={20} className="text-red-500" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}

export default EditModal;
