import express from "express";
import multer from "multer";
import fs from "fs";
import { 
    addAttributeAssociation, 
    removeAttributeAssociation 
} from "../controllers/attributeAssociationController";
import { 
    getAttributes, 
    createAttribute,
    getAttributeById,
    deleteAttribute, 
    uploadAttributeImage as uploadAttributeImageController 
} from "../controllers/attributeController";
import { 
    getEntities, 
    createEntity, 
    deleteEntity, 
    updateEntity, 
    updateEntityImage 
} from "../controllers/entityController";

const router = express.Router();

const ensureDirectoryExists = (path: string) => {
    if (!fs.existsSync(path)) {
        fs.mkdirSync(path, { recursive: true });
    }
};

const entityStorage = multer.diskStorage({
    destination: (req, file, cb) => {
        const uploadPath = `DataLake/media/${process.env.CLIENT_NAME}/Entities/`;
        ensureDirectoryExists(uploadPath);
        cb(null, uploadPath);
    },
    filename: (req, file, cb) => {
        const entityId = req.params.id || Date.now();
        cb(null, `${entityId}.jpg`);
    }
});

const attributeStorage = multer.diskStorage({
    destination: (req, file, cb) => {
        const uploadPath = `DataLake/media/${process.env.CLIENT_NAME}/Entities/`;
        ensureDirectoryExists(uploadPath);
        cb(null, uploadPath);
    },
    filename: (req, file, cb) => {
        const attributeId = req.params.id || Date.now();
        cb(null, `${attributeId}.jpg`);
    }
});

// Upload handlers
const uploadEntityImage = multer({ storage: entityStorage }).single("image");
const uploadAttributeImageMiddleware = multer({ storage: attributeStorage }).single("image");

// Entity Routes
router.get("/entities", getEntities);
router.post("/entities", createEntity);
router.delete("/entities/:id", deleteEntity);
router.post("/entities/:id/image", uploadEntityImage, updateEntityImage);
router.put("/entities/:id", updateEntity);
router.put("/entities/:id/image", uploadEntityImage, updateEntityImage);

// Attribute Routes
router.get("/attributes", getAttributes);
router.post("/attributes", uploadAttributeImageMiddleware, createAttribute);
router.delete("/attributes/:id", deleteAttribute);
router.get("/attribute/:id", getAttributeById);
router.post("/attributes/:id/image", uploadAttributeImageMiddleware, uploadAttributeImageController);

// Attribute Associations
router.post("/attribute-associations", addAttributeAssociation);
router.delete("/attribute-associations/:id", removeAttributeAssociation);

export default router;
