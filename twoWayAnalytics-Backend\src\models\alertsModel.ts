export interface Alert {
  id?: number;
  channel: string;
  publicationId: number;
  entityId: number;
  attributeId: number;
  duration: number;
  repeat: boolean;
  startDate: string;
  endDate: string;
  metricType: string;
  metrics: string;
  threshold: number;
  above: boolean;
  comments: number; // ✅ Ensure this is a number
  mention: string;
  metricsCode: string;
  status: string;
  alertChannel: string;
  alertCount: number;
  lastAlertDate: string;
  createDate: string;
  validated: boolean;
  alertToSend: boolean;
  alertSent: boolean;
  currentValue: number;
}
