// import { useEffect, useState } from "react";
// import { useTranslation } from "react-i18next";
// import {
//   FaEye,
//   FaFacebookF,
//   FaInstagram,
//   FaLink,
//   FaXTwitter,
// } from "react-icons/fa6";
// import { MdAddCircleOutline, MdFilterList } from "react-icons/md";
// import { RiYoutubeFill } from "react-icons/ri";
// import { useNavigate } from "react-router-dom";
// import linkedinIcon from "../assets/Linkedin.svg";
// import Dropdown from "../components/Dropdown";
// import Modal from "../components/Modal";
// import Pagination from "../components/Pagination";
// import SocialNetworkChips from "../components/SocialNetworkChips";
// import apiService from "../services/apiService";
// import Loader from "./loader";
// import TagModal from "./tagModal";
// import React from "react";
// import { IoIosSearch } from "react-icons/io";

// interface Association {
//   AttributeId: string;
//   Key: string;
//   Value: string;
// }

// interface Publication {
//   PublicationID: number;
//   Posts: {
//     Thumbnail: string;
//     Text: string;
//     SocialNetwork: string;
//     URL: string;
//     TimeStamp: string;
//   }[];
//   Validation: string;
//   NumberOfAssociations: number;
// }

// function Publications() {
//   const navigate = useNavigate();
//   const { t } = useTranslation();
//   const [data, setData] = useState<Publication[]>([]);
//   const [originalData, setOriginalData] = useState<Publication[]>([]);
//   const [loading, setLoading] = useState(false);
//   const [dateFilter, setDateFilter] = useState(() => localStorage.getItem("publicationsDateFilter") || "");
//   const [startDate, setStartDate] = useState(() => localStorage.getItem("publicationsStartDate") || "");
//   const [endDate, setEndDate] = useState(() => localStorage.getItem("publicationsEndDate") || "");
//   const [postType, setPostType] = useState(() => localStorage.getItem("publicationsPostType") || "");
//   const [socialNetwork, setSocialNetwork] = useState<string[]>(() => JSON.parse(localStorage.getItem("publicationsSocialNetwork") || "[]"));
//   const [searchTerm, setSearchTerm] = useState(() => localStorage.getItem("publicationsSearchTerm") || "");
//   const [isTagModalOpen, setIsTagModalOpen] = useState(false);
//   const [selectedPublicationId, setSelectedPublicationId] = useState<number | null>(null);
//   const [tags, setTags] = useState<string[]>([]);
//   const [tagInput, setTagInput] = useState("");
//   const [sortField, setSortField] = useState<"text" | "date">(() => localStorage.getItem("publicationsSortField") as "text" | "date" || "date");
//   const [sortDirection, setSortDirection] = useState<"asc" | "desc">(() => localStorage.getItem("publicationsSortDirection") as "asc" | "desc" || "desc");
//   const [isEntitiesModalOpen, setIsEntitiesModalOpen] = useState(false);
//   const [entitiesLoading, setEntitiesLoading] = useState(false);
//   const [entitiesData, setEntitiesData] = useState<Association[]>([]);
//   const [itemsPerPage] = useState(10);
//   const [currentPage, setCurrentPage] = useState(() => {
//     const savedPage = localStorage.getItem("publicationsCurrentPage");
//     return savedPage ? parseInt(savedPage, 10) : 1;
//   });

//   // Save filter states to localStorage
//   useEffect(() => {
//     localStorage.setItem("publicationsDateFilter", dateFilter);
//   }, [dateFilter]);

//   useEffect(() => {
//     localStorage.setItem("publicationsStartDate", startDate);
//   }, [startDate]);

//   useEffect(() => {
//     localStorage.setItem("publicationsEndDate", endDate);
//   }, [endDate]);

//   useEffect(() => {
//     localStorage.setItem("publicationsPostType", postType);
//   }, [postType]);

//   useEffect(() => {
//     localStorage.setItem("publicationsSocialNetwork", JSON.stringify(socialNetwork));
//   }, [socialNetwork]);

//   useEffect(() => {
//     localStorage.setItem("publicationsSearchTerm", searchTerm);
//   }, [searchTerm]);

//   useEffect(() => {
//     localStorage.setItem("publicationsSortField", sortField);
//   }, [sortField]);

//   useEffect(() => {
//     localStorage.setItem("publicationsSortDirection", sortDirection);
//   }, [sortDirection]);

//   useEffect(() => {
//     localStorage.setItem("publicationsCurrentPage", currentPage.toString());
//   }, [currentPage]);

//   // Validate currentPage when data changes
//   useEffect(() => {
//     const maxPage = Math.ceil(data.length / itemsPerPage);
//     if (currentPage > maxPage && maxPage > 0) {
//       setCurrentPage(1);
//       localStorage.setItem("publicationsCurrentPage", "1");
//     }
//   }, [data, currentPage, itemsPerPage]);

//   // Restore and apply filters on mount
//   useEffect(() => {
//     const savedDateFilter = localStorage.getItem("publicationsDateFilter") || "";
//     if (["daily", "weekly", "monthly"].includes(savedDateFilter)) {
//       handleDateFilterChange(savedDateFilter);
//     }
//     fetchFilteredData();
//   }, []);

//   // Apply search whenever searchTerm changes
//   useEffect(() => {
//     if (searchTerm !== "") {
//       const filtered = originalData.filter((post) => {
//         const searchLower = searchTerm.toLowerCase();
//         return (
//           (post.Posts?.[0]?.Text?.toLowerCase().includes(searchLower)) ||
//           (post.Validation?.toLowerCase().includes(searchLower)) ||
//           (post.Posts?.some((p: any) => p.SocialNetwork?.toLowerCase().includes(searchLower)))
//         );
//       });
//       setData(filtered);
//       setCurrentPage(1);
//       localStorage.setItem("publicationsCurrentPage", "1");
//     } else {
//       setData(originalData);
//     }
//   }, [originalData, searchTerm]);

//   const getValueByField = (item: Publication, field: "text" | "date") => {
//     if (field === "text") {
//       return item?.Posts?.[0]?.Text?.toLowerCase() || "";
//     }
//     if (field === "date") {
//       return new Date(item?.Posts?.[0]?.TimeStamp || 0).getTime();
//     }
//     return "";
//   };

//   const sortedData = [...data].sort((a, b) => {
//     const aValue = getValueByField(a, sortField);
//     const bValue = getValueByField(b, sortField);

//     if (aValue < bValue) return sortDirection === "asc" ? -1 : 1;
//     if (aValue > bValue) return sortDirection === "asc" ? 1 : -1;
//     return 0;
//   });

//   const totalItems = sortedData.length;
//   const startIndex = (currentPage - 1) * itemsPerPage;
//   const endIndex = Math.min(startIndex + itemsPerPage, totalItems);
//   const paginatedData = sortedData.slice(startIndex, endIndex);

//   const handleAddTag = async () => {
//     if (tagInput.trim() !== "" && selectedPublicationId !== null) {
//       const newTag = tagInput.trim();
//       console.log("Adding tag:", newTag);

//       try {
//         const response = await apiService.post(
//           `attribute-associations/tags/associate`,
//           {
//             publicationId: selectedPublicationId,
//             tag: newTag,
//           }
//         );
//         console.log("Tag added successfully:", response);

//         setTags([...tags, newTag]);
//       } catch (error) {
//         console.error("Error adding tag:", error);
//         alert("Failed to add tag.");
//       } finally {
//         setTagInput("");
//       }
//     }
//   };

//   const closeTagModal = () => {
//     setIsTagModalOpen(false);
//     setTags([]);
//     setTagInput("");
//   };

//   const fetchEntitiesData = async (publicationId: number) => {
//     setEntitiesLoading(true);
//     try {
//       const response = await apiService.get<{ associations: Association[] }>(
//         `/attribute-associations/publication-attribute-association?publicationId=${publicationId}`
//       );
//       setEntitiesData(response.associations || []);
//     } catch (error) {
//       console.error("Error fetching entities data:", error);
//     } finally {
//       setEntitiesLoading(false);
//     }
//   };

//   const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
//     setSearchTerm(e.target.value);
//   };

//   const fetchFilteredData = async () => {
//     setLoading(true);
//     try {
//       let filteredParams: any = {
//         validation: postType,
//         socialNetwork: socialNetwork.join(","),
//       };

//       if (dateFilter === "custom") {
//         filteredParams.startDate = startDate;
//         filteredParams.endDate = endDate;
//       } else if (dateFilter === "daily") {
//         const today = new Date();
//         today.setHours(0, 0, 0, 0);
//         filteredParams.startDate = today.toISOString();
//         today.setHours(23, 59, 59, 999);
//         filteredParams.endDate = today.toISOString();
//       } else if (dateFilter === "weekly") {
//         const today = new Date();
//         const lastWeek = new Date(today);
//         lastWeek.setDate(today.getDate() - 6);
//         lastWeek.setHours(0, 0, 0, 0);
//         today.setHours(23, 59, 59, 999);
//         filteredParams.startDate = lastWeek.toISOString();
//         filteredParams.endDate = today.toISOString();
//       } else if (dateFilter === "monthly") {
//         const today = new Date();
//         const lastMonth = new Date(today);
//         lastMonth.setDate(today.getDate() - 29);
//         lastMonth.setHours(0, 0, 0, 0);
//         today.setHours(23, 59, 59, 999);
//         filteredParams.startDate = lastMonth.toISOString();
//         filteredParams.endDate = today.toISOString();
//       }

//       filteredParams = Object.fromEntries(
//         Object.entries(filteredParams).filter(([_, v]) => v !== "" && v !== undefined)
//       );

//       const response = await apiService.getfilter<Publication[]>(
//         "/attribute-associations/publications",
//         filteredParams
//       );
//       setData(response);
//       setOriginalData(response);
//     } catch (error) {
//       setData([]);
//       setOriginalData([]);
//       console.error("Error fetching filtered data:", error);
//     } finally {
//       setLoading(false);
//     }
//   };

//   const handleFilter = () => {
//     fetchFilteredData();
//     setCurrentPage(1);
//     localStorage.setItem("publicationsCurrentPage", "1");
//   };

//   const handleDateFilterChange = (value: string) => {
//     setDateFilter(value);
//     if (value !== "custom") {
//       const today = new Date();
//       let start: Date;
//       let end: Date = new Date(today);
//       end.setHours(23, 59, 59, 999);

//       if (value === "daily") {
//         start = new Date(today);
//         start.setHours(0, 0, 0, 0);
//       } else if (value === "weekly") {
//         start = new Date(today);
//         start.setDate(today.getDate() - 6);
//         start.setHours(0, 0, 0, 0);
//       } else if (value === "monthly") {
//         start = new Date(today);
//         start.setDate(today.getDate() - 29);
//         start.setHours(0, 0, 0, 0);
//       } else {
//         setStartDate("");
//         setEndDate("");
//         return;
//       }

//       setStartDate(start.toISOString().split('T')[0]);
//       setEndDate(end.toISOString().split('T')[0]);
//     }
//   };

//   const sortData = (field: "text" | "date") => {
//     const direction = sortField === field && sortDirection === "asc" ? "desc" : "asc";
//     setSortField(field);
//     setSortDirection(direction);
//   };

//   const openEntitiesModal = (publicationId: number) => {
//     setSelectedPublicationId(publicationId);
//     fetchEntitiesData(publicationId);
//     setIsEntitiesModalOpen(true);
//   };

//   const handlePageChange = (newPage: number) => {
//     if (newPage >= 1 && newPage <= Math.ceil(data.length / itemsPerPage)) {
//       setCurrentPage(newPage);
//     }
//   };

//   const handleLinkNavigation = (publicationId: number) => {
//     navigate(`/linkentitiestopost/${publicationId}`);
//   };

//   const options = [
//     { value: "", label: "All" },
//     { value: "Validated", label: "Validated" },
//     { value: "Unvalidated", label: "Unvalidated" },
//     { value: "Partially Validated", label: "Partially Validated" },
//   ];

//   const dateFilterOptions = [
//     { value: "", label: "All" },
//     { value: "daily", label: t("group-posts.daily_posts") },
//     { value: "weekly", label: t("group-posts.last_week_posts") },
//     { value: "monthly", label: t("group-posts.last_month_posts") },
//     { value: "custom", label: t("group-posts.custom_date") },
//   ];

//   return (
//     <div className="bg-white rounded-3xl shadow xs:max-sm:mt-4 pt-4">
//       <div className={`flex flex-col xs:max-sm:px-4 sm:px-4`}>
//         <div className="w-full mb-4">
//           <div className="relative w-full">
//             <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
//               <IoIosSearch className="text-gray-400" />
//             </div>
//             <input
//               type="text"
//               placeholder={t("publication.SearchPosts") || "Search posts..."}
//               className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-3xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
//               value={searchTerm}
//               onChange={handleSearch}
//             />
//           </div>
//         </div>

//         <div className="flex flex-col sm:flex-row sm:flex-wrap gap-4 w-full">
//           <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px]">
//             <label htmlFor="date-filter" className="text-[#5A5A5A] mb-2 block">
//               {t("publication.date_filter")}
//             </label>
//             <Dropdown
//               value={dateFilter}
//               onChange={handleDateFilterChange}
//               options={dateFilterOptions}
//               placeholder="Select time frame"
//               className="rounded-3xl h-[47px] w-full"
//             />
//           </div>

//           {dateFilter === "custom" && (
//             <>
//               <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px]">
//                 <label htmlFor="start-date" className="text-[#5A5A5A] mb-2 block">
//                   {t("group-posts.begin")}
//                 </label>
//                 <input
//                   id="start-date"
//                   type="date"
//                   value={startDate}
//                   onChange={(e) => setStartDate(e.target.value)}
//                   className="w-full rounded-lg border border-gray-200 bg-white px-3 h-[47px] py-2 text-gray-900"
//                 />
//               </div>

//               <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px]">
//                 <label htmlFor="end-date" className="text-[#5A5A5A] mb-2 block">
//                   {t("group-posts.end")}
//                 </label>
//                 <input
//                   id="end-date"
//                   type="date"
//                   value={endDate}
//                   onChange={(e) => setEndDate(e.target.value)}
//                   className="w-full rounded-lg border border-gray-200 bg-white px-3 h-[47px] py-2 text-gray-900"
//                 />
//               </div>
//             </>
//           )}

//           <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px]">
//             <label htmlFor="type" className="text-[#5A5A5A] mb-2 block">
//               {t("group-posts.type")}
//             </label>
//             <Dropdown
//               value={postType}
//               onChange={setPostType}
//               options={options}
//               placeholder="Select post type"
//               className="rounded-3xl h-[47px] w-full"
//             />
//           </div>

//           <div className="w-full sm:flex-1 flex flex-col sm:flex-row items-end gap-4 mt-2 sm:mt-0">
//             <div className="w-full sm:flex-1">
//               <SocialNetworkChips
//                 setSocialNetwork={setSocialNetwork}
//                 socialNetwork={socialNetwork}
//                 t={t}
//               />
//             </div>
//             <div className="w-full sm:w-auto">
//               <button
//                 onClick={handleFilter}
//                 className="bg-secondary w-full sm:w-auto flex items-center justify-center gap-2 btn text-[#ffffff] py-2 px-10 text-lg h-[47px] font-light rounded-xl"
//               >
//                 <MdFilterList />
//                 {t("group-posts.button")}
//               </button>
//             </div>
//           </div>
//         </div>
//       </div>

//       <div className="mt-6 xs:max-sm:hidden rounded-xl">
//         <div className="bg-white overflow-hidden">
//           <div className="w-full overflow-x-auto">
//             <table className="min-w-full border-collapse text-left">
//               <thead>
//                 <tr className="border-b border-gray-200 font-bold tracking-tight bg-[#EDF1F575] text-sm">
//                   <th className="left-col">{t("thumbnail")}</th>
//                   <th className="p-4 cursor-pointer" onClick={() => sortData("text")}>
//                     <div className="flex items-center">
//                       {t("publication.text")}
//                       <span className="ml-1 text-xs leading-none pl-1">
//                         {sortField === "text"
//                           ? sortDirection === "asc"
//                             ? "↑"
//                             : "↓"
//                           : "↑↓"}
//                       </span>
//                     </div>
//                   </th>
//                   <th className="p-4">{t("publication.socialMedia")}</th>
//                   <th className="p-4">{t("publication.status")}</th>
//                   <th className="px-4 py-4 cursor-pointer" onClick={() => sortData("date")}>
//                     <div className="flex items-center">
//                       {t("publication.createdTime")}
//                       <span className="ml-1 text-xs leading-none pl-1">
//                         {sortField === "date"
//                           ? sortDirection === "asc"
//                             ? "↑"
//                             : "↓"
//                           : "↑↓"}
//                       </span>
//                     </div>
//                   </th>
//                   <th className="p-4 text-nowrap">
//                     {t("publication.linkCount")}
//                   </th>
//                   <th className="right-col">{t("publication.actions")}</th>
//                 </tr>
//               </thead>
//               <tbody>
//                 {loading ? (
//                   <tr>
//                     <td colSpan={5}>
//                       <Loader />
//                     </td>
//                   </tr>
//                 ) : paginatedData.length > 0 ? (
//                   paginatedData.map((item, index) => (
//                     <tr
//                       key={index}
//                       className="border-b border-gray-100 hover:bg-gray-50"
//                     >
//                       <td className="left-col">
//                         {item.Posts?.[0]?.Thumbnail && (
//                           <img
//                             src={
//                               item.Posts[0].Thumbnail ||
//                               "https://img.freepik.com/premium-vector/vector-flat-illustration-grayscale-avatar-user-profile-person-icon-profile-picture-business-profile-woman-suitable-social-media-profiles-icons-screensavers-as-templatex9_719432-1351.jpg?semt=ais_hybrid&w=740"
//                             }
//                             alt="Thumbnail"
//                             className="w-10 h-10 rounded-xl"
//                           />
//                         )}
//                       </td>
//                       <td
//                         className="py-4 px-4 text-[0.85rem] text-nowrap truncate max-w-[150px] lg:min-w-[260px]"
//                         title={item?.Posts?.[0]?.Text || "N/A"}
//                       >
//                         {item?.Posts?.[0]?.Text || "N/A"}
//                       </td>
//                       <td className="p-4 min-w-[100px]">
//                         <div className="flex items-center space-x-0.5 flex-wrap">
//                           {item.Posts?.map((post: any) => {
//                             let icon;
//                             switch (post.SocialNetwork) {
//                               case "Facebook":
//                                 icon = (
//                                   <a
//                                     href={post.URL}
//                                     target="_blank"
//                                     rel="noopener noreferrer"
//                                   >
//                                     <FaFacebookF
//                                       className="text-[1.50rem] bg-[#1877F2] p-1.5 text-[#fff] rounded-lg"
//                                       style={{
//                                         boxShadow: "0px 3px 10px 0px #1976D252",
//                                       }}
//                                     />
//                                   </a>
//                                 );
//                                 break;
//                               case "Instagram":
//                                 icon = (
//                                   <a
//                                     href={post.URL}
//                                     target="_blank"
//                                     rel="noopener noreferrer"
//                                   >
//                                     <FaInstagram
//                                       className="text-[1.50rem] p-1.5 text-white rounded-lg"
//                                       style={{
//                                         background:
//                                           "linear-gradient(45deg, #feda75, #fa7e1e, #d62976, #962fbf, #4f5bd5)",
//                                         boxShadow:
//                                           "0px 4px 4px 0px rgba(0, 0, 0, 0.2)",
//                                       }}
//                                     />
//                                   </a>
//                                 );
//                                 break;
//                               case "LinkedIn":
//                                 icon = linkedinIcon;
//                                 break;
//                               case "Twitter":
//                                 icon = (
//                                   <a
//                                     href={post.URL}
//                                     target="_blank"
//                                     rel="noopener noreferrer"
//                                   >
//                                     <FaXTwitter
//                                       className="text-[1.50rem] bg-[#000] p-1.5 text-[#fff] rounded-lg"
//                                       style={{
//                                         boxShadow:
//                                           "0px 4px 8px rgba(0, 0, 0, 0.2)",
//                                       }}
//                                     />
//                                   </a>
//                                 );
//                                 break;
//                               case "Youtube":
//                                 icon = (
//                                   <a
//                                     href={post.URL}
//                                     target="_blank"
//                                     rel="noopener noreferrer"
//                                   >
//                                     <RiYoutubeFill
//                                       className="text-[1.50rem] bg-[#E21A20] p-1.5 text-[#fff] rounded-lg"
//                                       style={{
//                                         boxShadow: "0px 4px 4px 0px #E21A204F",
//                                       }}
//                                     />
//                                   </a>
//                                 );
//                                 break;
//                               default:
//                                 return null;
//                             }
//                             return icon;
//                           })}
//                         </div>
//                       </td>
//                       <td className="p-4 text-sm">
//                         <span
//                           className={`px-2 py-1.5 rounded-lg text-[0.85rem] font-normal ${item.Validation === "Validated"
//                             ? "bg-lightblue text-primary"
//                             : item.Validation === "Unvalidated"
//                               ? "bg-red-100 text-red-600"
//                               : "bg-blue-100 text-blue-600"
//                             }`}
//                         >
//                           {item.Validation || "Pending"}
//                         </span>
//                       </td>
//                       <td className="p-4 text-[0.85rem]">
//                         <span>
//                           {new Date(
//                             item.Posts[0].TimeStamp
//                           ).toLocaleDateString()}
//                         </span>
//                       </td>
//                       <td className="p-4 text-[0.85rem]">{item.NumberOfAssociations}</td>
//                       <td className="right-col">
//                         <div className="flex items-center gap-1">
//                           <button
//                             className="px-2 py-2 rounded-lg bg-lightblue cursor-pointer text-sm text-primary"
//                             onClick={() => openEntitiesModal(item.PublicationID)}
//                           >
//                             <FaEye />
//                           </button>

//                           <button
//                             className="px-2 py-2 rounded-lg bg-[#21CE9E1A] cursor-pointer text-sm text-[#21CE9E]"
//                             onClick={() => handleLinkNavigation(item.PublicationID)}
//                           >
//                             <FaLink />
//                           </button>

//                           <button
//                             className="px-2 py-2 rounded-lg text-sm cursor-pointer text-[#FB4242] bg-[#FB42421A]"
//                             onClick={() => {
//                               setSelectedPublicationId(item.PublicationID);
//                               setIsTagModalOpen(true);
//                             }}
//                           >
//                             <MdAddCircleOutline />
//                           </button>
//                         </div>
//                       </td>
//                     </tr>
//                   ))
//                 ) : (
//                   <tr>
//                     <td colSpan={5} className="text-center py-4">
//                       No data available
//                     </td>
//                   </tr>
//                 )}
//               </tbody>
//             </table>
//             <div className="mt-4 px-6">
//               <Pagination
//                 currentPage={currentPage}
//                 totalItems={data.length}
//                 itemsPerPage={itemsPerPage}
//                 onPageChange={handlePageChange}
//               />
//             </div>
//             {data.length === 0 && (
//               <p className="text-center text-gray-500 py-4">
//                 {t("No publications found")}
//               </p>
//             )}
//           </div>
//         </div>
//       </div>
//       {/* Mobile UI */}
//       <div className="mt-6 rounded-xl hidden xs:max-sm:block">
//         <div className="bg-white overflow-hidden">
//           <div className="overflow-x-auto relative">
//             <table className="min-w-full border-collapse text-left text-sm">
//               <thead>
//                 <tr className="border-b border-gray-200 font-bold tracking-tight bg-[#EDF1F575] text-sm">
//                   <th className="px-6 py-5">{t("thumbnail")}</th>
//                 </tr>
//               </thead>
//               <tbody className="flex flex-col">
//                 {loading ? (
//                   <tr>
//                     <td colSpan={5}>
//                       <Loader />
//                     </td>
//                   </tr>
//                 ) : paginatedData.length > 0 ? (
//                   paginatedData.map((item: any) => (
//                     <React.Fragment key={item.PublicationID}>
//                       <tr className="border-b border-gray-100 hover:bg-gray-50">
//                         <span className="py-4 px-8 flex justify-between w-full">
//                           <td className="">
//                             {item.Posts?.[0]?.Thumbnail && (
//                               <img
//                                 src={
//                                   item.Posts[0].Thumbnail
//                                     ? item.Posts[0].Thumbnail
//                                     : "https://img.freepik.com/premium-vector/vector-flat-illustration-grayscale-avatar-user-profile-person-icon-profile-picture-business-profile-woman-suitable-social-media-profiles-icons-screensavers-as-templatex9_719432-1351.jpg?semt=ais_hybrid&w=740"
//                                 }
//                                 alt="Thumbnail"
//                                 className="w-15 h-15 rounded-xl"
//                               />
//                             )}
//                           </td>
//                           <td className="flex flex-col items-end">
//                             <td className="flex w-full gap-2 flex justify-end items-center">
//                               <button
//                                 className="px-2 py-2 rounded-lg mt-3.5 text-md bg-[#21CE9E1A] cursor-pointer text-xl text-[#21CE9E]"
//                                 onClick={() => openEntitiesModal(item.PublicationID)}
//                               >
//                                 <FaEye />
//                               </button>
//                               <button
//                                 className="px-2 py-2 rounded-lg mt-3.5 text-md cursor-pointer text-[#FB4242] bg-[#FB42421A]"
//                                 onClick={() => handleLinkNavigation(item.PublicationID)}
//                               >
//                                 <FaLink />
//                               </button>
//                               <button
//                                 className="px-2 py-2 rounded-lg mt-3.5 text-md bg-lightblue cursor-pointer text-primary"
//                                 onClick={() => {
//                                   setSelectedPublicationId(item.PublicationID);
//                                   setIsTagModalOpen(true);
//                                 }}
//                               >
//                                 <MdAddCircleOutline />
//                               </button>
//                             </td>
//                             <td className="flex mt-3 items-end justify-end space-x-2">
//                               {item.Posts?.map((post: any, i: number) => {
//                                 let icon;
//                                 switch (post.SocialNetwork) {
//                                   case "Facebook":
//                                     icon = (
//                                       <span className="h-6 w-6">
//                                         <FaFacebookF
//                                           className="text-[1.78rem] bg-[#1877F2] p-1.5 text-[#fff] rounded-lg"
//                                           style={{
//                                             boxShadow:
//                                               "0px 3px 10px 0px #1976D252",
//                                           }}
//                                         />
//                                       </span>
//                                     );
//                                     break;
//                                   case "Instagram":
//                                     icon = (
//                                       <span className="h-6 w-6">
//                                         <FaInstagram
//                                           className="text-[1.78rem] p-1.5 text-white rounded-lg"
//                                           style={{
//                                             background:
//                                               "linear-gradient(45deg, #feda75, #fa7e1e, #d62976, #962fbf, #4f5bd5)",
//                                             boxShadow:
//                                               "0px 4px 4px 0px rgba(0, 0, 0, 0.2)",
//                                           }}
//                                         />
//                                       </span>
//                                     );
//                                     break;
//                                   case "LinkedIn":
//                                     icon = linkedinIcon;
//                                     break;
//                                   case "Twitter":
//                                     icon = (
//                                       <span className="h-6 w-6">
//                                         <FaXTwitter
//                                           className="text-[1.78rem] bg-[#000] p-1.5 text-[#fff] rounded-lg"
//                                           style={{
//                                             boxShadow:
//                                               "0px 4px 8px rgba(0, 0, 0, 0.2)",
//                                           }}
//                                         />
//                                       </span>
//                                     );
//                                     break;
//                                   case "Youtube":
//                                     icon = (
//                                       <span className="h-6 w-6">
//                                         <RiYoutubeFill
//                                           className="text-[1.78rem] bg-[#E21A20] p-1.5 text-[#fff] rounded-lg"
//                                           style={{
//                                             boxShadow:
//                                               "0px 4px 4px 0px #E21A204F",
//                                           }}
//                                         />
//                                       </span>
//                                     );
//                                     break;
//                                   default:
//                                     return null;
//                                 }

//                                 return (
//                                   <a
//                                     key={i}
//                                     href={post.URL}
//                                     target="_blank"
//                                     rel="noopener noreferrer"
//                                   >
//                                     {icon}
//                                   </a>
//                                 );
//                               })}
//                             </td>
//                           </td>
//                         </span>

//                         <span className="flex items-center justify-between">
//                           <td className="py-4 px-8 text-sm">
//                             {new Date(
//                               item.Posts[0].TimeStamp
//                             ).toLocaleDateString()}
//                           </td>
//                           <td className="py-4 px-8 text-sm">
//                             {item.NumberOfAssociations}
//                           </td>
//                         </span>
//                         <td
//                           className="py-4 px-8 max-w-[200px] text-sm text-nowrap truncate"
//                           title={item?.Posts?.[0]?.Text || "N/A"}
//                         >
//                           {item?.Posts?.[0]?.Text || "N/A"}
//                         </td>
//                       </tr>
//                     </React.Fragment>
//                   ))
//                 ) : (
//                   <tr>
//                     <td colSpan={5} className="text-center py-4">
//                       No data available
//                     </td>
//                   </tr>
//                 )}
//               </tbody>
//             </table>
//             {!loading && (
//               <div className="mt-4 px-8">
//                 <Pagination
//                   currentPage={currentPage}
//                   totalItems={data.length}
//                   itemsPerPage={itemsPerPage}
//                   onPageChange={handlePageChange}
//                 />
//               </div>
//             )}
//           </div>
//         </div>
//       </div>

//       {/* Tag Modal */}
//       <TagModal
//         isOpen={isTagModalOpen}
//         tagInput={tagInput}
//         onChangeInput={setTagInput}
//         onAddTag={handleAddTag}
//         onClose={closeTagModal}
//         publicationId={
//           selectedPublicationId !== null
//             ? String(selectedPublicationId)
//             : undefined
//         }
//       />

//       {/* Entities Related Modal */}
//       <Modal
//         isOpen={isEntitiesModalOpen}
//         onClose={() => setIsEntitiesModalOpen(false)}
//         label="Entities Related to Publication"
//       >
//         {entitiesLoading ? (
//           <p className="text-center p-4">Loading...</p>
//         ) : entitiesData.length > 0 ? (
//           <div className="overflow-x-auto mt-2">
//             <table className="min-w-full border-collapse text-left">
//               <thead>
//                 <tr className="border-b border-gray-200 text-[#9C9C9C] font-normal text-sm">
//                   <th className="p-3">Key</th>
//                   <th className="p-3 text-nowrap">Value</th>
//                 </tr>
//               </thead>
//               <tbody>
//                 {entitiesData.map((item) => (
//                   <tr
//                     key={item.AttributeId}
//                     className="border-b border-gray-100 hover:bg-gray-50"
//                   >
//                     <td className="p-3 font-medium text-nowrap">{item.Key}</td>
//                     <td className="p-3 font-medium text-nowrap">
//                       {item.Value}
//                     </td>
//                   </tr>
//                 ))}
//               </tbody>
//             </table>
//           </div>
//         ) : (
//           <p className="text-center p-4">No data available.</p>
//         )}
//       </Modal>
//     </div>
//   );
// }

// export default Publications;

import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import {
  FaEye,
  FaFacebookF,
  FaInstagram,
  FaLink,
  FaXTwitter,
} from "react-icons/fa6";
import { MdAddCircleOutline, MdFilterList } from "react-icons/md";
import { RiYoutubeFill } from "react-icons/ri";
import { useNavigate } from "react-router-dom";
import linkedinIcon from "../assets/Linkedin.svg";
import Dropdown from "../components/Dropdown";
import Modal from "../components/Modal";
import Pagination from "../components/Pagination";
import SocialNetworkChips from "../components/SocialNetworkChips";
import apiService from "../services/apiService";
import Loader from "./loader";
import TagModal from "./tagModal";
import React from "react";
import { IoIosSearch } from "react-icons/io";

interface Association {
  AttributeId: string;
  Key: string;
  Value: string;
}

interface Publication {
  PublicationID: number;
  Posts: {
    Thumbnail: string;
    Text: string;
    SocialNetwork: string;
    URL: string;
    TimeStamp: string;
  }[];
  Validation: string;
  NumberOfAssociations: number;
}

function Publications() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [data, setData] = useState<Publication[]>([]);
  const [originalData, setOriginalData] = useState<Publication[]>([]);
  const [loading, setLoading] = useState(false);
  const [dateFilter, setDateFilter] = useState(() => localStorage.getItem("publicationsDateFilter") || "");
  const [startDate, setStartDate] = useState(() => localStorage.getItem("publicationsStartDate") || "");
  const [endDate, setEndDate] = useState(() => localStorage.getItem("publicationsEndDate") || "");
  const [postType, setPostType] = useState(() => localStorage.getItem("publicationsPostType") || "");
  const [socialNetwork, setSocialNetwork] = useState<string[]>(() => JSON.parse(localStorage.getItem("publicationsSocialNetwork") || "[]"));
  const [searchTerm, setSearchTerm] = useState(() => localStorage.getItem("publicationsSearchTerm") || "");
  const [isTagModalOpen, setIsTagModalOpen] = useState(false);
  const [selectedPublicationId, setSelectedPublicationId] = useState<number | null>(null);
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState("");
  const [sortField, setSortField] = useState<"text" | "date">(() => localStorage.getItem("publicationsSortField") as "text" | "date" || "date");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">(() => localStorage.getItem("publicationsSortDirection") as "asc" | "desc" || "desc");
  const [isEntitiesModalOpen, setIsEntitiesModalOpen] = useState(false);
  const [entitiesLoading, setEntitiesLoading] = useState(false);
  const [entitiesData, setEntitiesData] = useState<Association[]>([]);
  const [itemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(() => {
    const savedPage = localStorage.getItem("publicationsCurrentPage");
    return savedPage ? parseInt(savedPage, 10) : 1;
  });

  // Translation options
  const options = [
    { value: "", label: t("publication.validationOptions.all") },
    { value: "Validated", label: t("publication.validationOptions.validated") },
    { value: "Unvalidated", label: t("publication.validationOptions.unvalidated") },
    { value: "Partially Validated", label: t("publication.validationOptions.partiallyValidated") },
  ];

  const dateFilterOptions = [
    { value: "", label: t("publication.validationOptions.all") },
    { value: "daily", label: t("publication.dailyPosts") },
    { value: "weekly", label: t("publication.lastWeekPosts") },
    { value: "monthly", label: t("publication.lastMonthPosts") },
    { value: "custom", label: t("publication.customDate") },
  ];

  // Save filter states to localStorage
  useEffect(() => {
    localStorage.setItem("publicationsDateFilter", dateFilter);
  }, [dateFilter]);

  useEffect(() => {
    localStorage.setItem("publicationsStartDate", startDate);
  }, [startDate]);

  useEffect(() => {
    localStorage.setItem("publicationsEndDate", endDate);
  }, [endDate]);

  useEffect(() => {
    localStorage.setItem("publicationsPostType", postType);
  }, [postType]);

  useEffect(() => {
    localStorage.setItem("publicationsSocialNetwork", JSON.stringify(socialNetwork));
  }, [socialNetwork]);

  useEffect(() => {
    localStorage.setItem("publicationsSearchTerm", searchTerm);
  }, [searchTerm]);

  useEffect(() => {
    localStorage.setItem("publicationsSortField", sortField);
  }, [sortField]);

  useEffect(() => {
    localStorage.setItem("publicationsSortDirection", sortDirection);
  }, [sortDirection]);

  useEffect(() => {
    localStorage.setItem("publicationsCurrentPage", currentPage.toString());
  }, [currentPage]);

  // Validate currentPage when data changes
  useEffect(() => {
    const maxPage = Math.ceil(data.length / itemsPerPage);
    if (currentPage > maxPage && maxPage > 0) {
      setCurrentPage(1);
      localStorage.setItem("publicationsCurrentPage", "1");
    }
  }, [data, currentPage, itemsPerPage]);

  // Restore and apply filters on mount
  useEffect(() => {
    const savedDateFilter = localStorage.getItem("publicationsDateFilter") || "";
    if (["daily", "weekly", "monthly"].includes(savedDateFilter)) {
      handleDateFilterChange(savedDateFilter);
    }
    fetchFilteredData();
  }, []);

  // Apply search whenever searchTerm changes
  useEffect(() => {
    if (searchTerm !== "") {
      const filtered = originalData.filter((post) => {
        const searchLower = searchTerm.toLowerCase();
        return (
          (post.Posts?.[0]?.Text?.toLowerCase().includes(searchLower)) ||
          (post.Validation?.toLowerCase().includes(searchLower)) ||
          (post.Posts?.some((p: any) => p.SocialNetwork?.toLowerCase().includes(searchLower)))
        );
      });
      setData(filtered);
      setCurrentPage(1);
      localStorage.setItem("publicationsCurrentPage", "1");
    } else {
      setData(originalData);
    }
  }, [originalData, searchTerm]);

  const getValueByField = (item: Publication, field: "text" | "date") => {
    if (field === "text") {
      return item?.Posts?.[0]?.Text?.toLowerCase() || "";
    }
    if (field === "date") {
      return new Date(item?.Posts?.[0]?.TimeStamp || 0).getTime();
    }
    return "";
  };

  const sortedData = [...data].sort((a, b) => {
    const aValue = getValueByField(a, sortField);
    const bValue = getValueByField(b, sortField);

    if (aValue < bValue) return sortDirection === "asc" ? -1 : 1;
    if (aValue > bValue) return sortDirection === "asc" ? 1 : -1;
    return 0;
  });

  const totalItems = sortedData.length;
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, totalItems);
  const paginatedData = sortedData.slice(startIndex, endIndex);

  const handleAddTag = async () => {
    if (tagInput.trim() !== "" && selectedPublicationId !== null) {
      const newTag = tagInput.trim();
      console.log("Adding tag:", newTag);

      try {
        const response = await apiService.post(
          `attribute-associations/tags/associate`,
          {
            publicationId: selectedPublicationId,
            tag: newTag,
          }
        );
        console.log("Tag added successfully:", response);

        setTags([...tags, newTag]);
      } catch (error) {
        console.error("Error adding tag:", error);
        alert(t("publication.tagAddError"));
      } finally {
        setTagInput("");
      }
    }
  };

  const closeTagModal = () => {
    setIsTagModalOpen(false);
    setTags([]);
    setTagInput("");
  };

  const fetchEntitiesData = async (publicationId: number) => {
    setEntitiesLoading(true);
    try {
      const response = await apiService.get<{ associations: Association[] }>(
        `/attribute-associations/publication-attribute-association?publicationId=${publicationId}`
      );
      setEntitiesData(response.associations || []);
    } catch (error) {
      console.error("Error fetching entities data:", error);
    } finally {
      setEntitiesLoading(false);
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const fetchFilteredData = async () => {
    setLoading(true);
    try {
      let filteredParams: any = {
        validation: postType,
        socialNetwork: socialNetwork.join(","),
      };

      if (dateFilter === "custom") {
        filteredParams.startDate = startDate;
        filteredParams.endDate = endDate;
      } else if (dateFilter === "daily") {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        filteredParams.startDate = today.toISOString();
        today.setHours(23, 59, 59, 999);
        filteredParams.endDate = today.toISOString();
      } else if (dateFilter === "weekly") {
        const today = new Date();
        const lastWeek = new Date(today);
        lastWeek.setDate(today.getDate() - 6);
        lastWeek.setHours(0, 0, 0, 0);
        today.setHours(23, 59, 59, 999);
        filteredParams.startDate = lastWeek.toISOString();
        filteredParams.endDate = today.toISOString();
      } else if (dateFilter === "monthly") {
        const today = new Date();
        const lastMonth = new Date(today);
        lastMonth.setDate(today.getDate() - 29);
        lastMonth.setHours(0, 0, 0, 0);
        today.setHours(23, 59, 59, 999);
        filteredParams.startDate = lastMonth.toISOString();
        filteredParams.endDate = today.toISOString();
      }

      filteredParams = Object.fromEntries(
        Object.entries(filteredParams).filter(([_, v]) => v !== "" && v !== undefined)
      );

      const response = await apiService.getfilter<Publication[]>(
        "/attribute-associations/publications",
        filteredParams
      );
      setData(response);
      setOriginalData(response);
    } catch (error) {
      setData([]);
      setOriginalData([]);
      console.error("Error fetching filtered data:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleFilter = () => {
    fetchFilteredData();
    setCurrentPage(1);
    localStorage.setItem("publicationsCurrentPage", "1");
  };

  const handleDateFilterChange = (value: string) => {
    setDateFilter(value);
    if (value !== "custom") {
      const today = new Date();
      let start: Date;
      let end: Date = new Date(today);
      end.setHours(23, 59, 59, 999);

      if (value === "daily") {
        start = new Date(today);
        start.setHours(0, 0, 0, 0);
      } else if (value === "weekly") {
        start = new Date(today);
        start.setDate(today.getDate() - 6);
        start.setHours(0, 0, 0, 0);
      } else if (value === "monthly") {
        start = new Date(today);
        start.setDate(today.getDate() - 29);
        start.setHours(0, 0, 0, 0);
      } else {
        setStartDate("");
        setEndDate("");
        return;
      }

      setStartDate(start.toISOString().split('T')[0]);
      setEndDate(end.toISOString().split('T')[0]);
    }
  };

  const sortData = (field: "text" | "date") => {
    const direction = sortField === field && sortDirection === "asc" ? "desc" : "asc";
    setSortField(field);
    setSortDirection(direction);
  };

  const openEntitiesModal = (publicationId: number) => {
    setSelectedPublicationId(publicationId);
    fetchEntitiesData(publicationId);
    setIsEntitiesModalOpen(true);
  };

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= Math.ceil(data.length / itemsPerPage)) {
      setCurrentPage(newPage);
    }
  };

  const handleLinkNavigation = (publicationId: number) => {
    navigate(`/linkentitiestopost/${publicationId}`);
  };

  return (
    <div className="bg-white rounded-3xl shadow xs:max-sm:mt-4 pt-4">
      <div className={`flex flex-col xs:max-sm:px-4 sm:px-4`}>
        <div className="w-full mb-4">
          <div className="relative w-full">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <IoIosSearch className="text-gray-400" />
            </div>
            <input
              type="text"
              placeholder={t("publication.searchPosts")}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-3xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              value={searchTerm}
              onChange={handleSearch}
            />
          </div>
        </div>

        <div className="flex flex-col sm:flex-row sm:flex-wrap gap-4 w-full">
          <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px]">
            <label htmlFor="date-filter" className="text-[#5A5A5A] mb-2 block">
              {t("publication.dateFilter")}
            </label>
            <Dropdown
              value={dateFilter}
              onChange={handleDateFilterChange}
              options={dateFilterOptions}
              placeholder={t("publication.dateFilter")}
              className="rounded-3xl h-[47px] w-full"
            />
          </div>

          {dateFilter === "custom" && (
            <>
              <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px]">
                <label htmlFor="start-date" className="text-[#5A5A5A] mb-2 block">
                  {t("publication.beginDate")}
                </label>
                <input
                  id="start-date"
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="w-full rounded-lg border border-gray-200 bg-white px-3 h-[47px] py-2 text-gray-900"
                />
              </div>

              <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px]">
                <label htmlFor="end-date" className="text-[#5A5A5A] mb-2 block">
                  {t("publication.endDate")}
                </label>
                <input
                  id="end-date"
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="w-full rounded-lg border border-gray-200 bg-white px-3 h-[47px] py-2 text-gray-900"
                />
              </div>
            </>
          )}

          <div className="w-full sm:w-[48%] lg:w-[150px] min-w-[150px]">
            <label htmlFor="type" className="text-[#5A5A5A] mb-2 block">
              {t("publication.postType")}
            </label>
            <Dropdown
              value={postType}
              onChange={setPostType}
              options={options}
              placeholder={t("publication.postType")}
              className="rounded-3xl h-[47px] w-full"
            />
          </div>

          <div className="w-full sm:flex-1 flex flex-col sm:flex-row items-end gap-4 mt-2 sm:mt-0">
            <div className="w-full sm:flex-1">
              <SocialNetworkChips
                setSocialNetwork={setSocialNetwork}
                socialNetwork={socialNetwork}
                t={t}
              />
            </div>
            <div className="w-full sm:w-auto">
              <button
                onClick={handleFilter}
                className="bg-secondary w-full sm:w-auto flex items-center justify-center gap-2 btn text-[#ffffff] py-2 px-10 text-lg h-[47px] font-light rounded-xl"
              >
                <MdFilterList />
                {t("publication.filterButton")}
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-6 xs:max-sm:hidden rounded-xl">
        <div className="bg-white overflow-hidden">
          <div className="w-full overflow-x-auto">
            <table className="min-w-full border-collapse text-left">
              <thead>
                <tr className="border-b border-gray-200 font-bold tracking-tight bg-[#EDF1F575] text-sm">
                  <th className="left-col">{t("publication.thumbnail")}</th>
                  <th className="p-4 cursor-pointer" onClick={() => sortData("text")}>
                    <div className="flex items-center">
                      {t("publication.text")}
                      <span className="ml-1 text-xs leading-none pl-1">
                        {sortField === "text"
                          ? sortDirection === "asc"
                            ? "↑"
                            : "↓"
                          : "↑↓"}
                      </span>
                    </div>
                  </th>
                  <th className="p-4">{t("publication.socialMedia")}</th>
                  <th className="p-4">{t("publication.status")}</th>
                  <th className="px-4 py-4 cursor-pointer" onClick={() => sortData("date")}>
                    <div className="flex items-center">
                      {t("publication.createdTime")}
                      <span className="ml-1 text-xs leading-none pl-1">
                        {sortField === "date"
                          ? sortDirection === "asc"
                            ? "↑"
                            : "↓"
                          : "↑↓"}
                      </span>
                    </div>
                  </th>
                  <th className="p-4 text-nowrap">
                    {t("publication.linkCount")}
                  </th>
                  <th className="right-col">{t("publication.actions")}</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan={7}>
                      <Loader />
                    </td>
                  </tr>
                ) : paginatedData.length > 0 ? (
                  paginatedData.map((item, index) => (
                    <tr
                      key={index}
                      className="border-b border-gray-100 hover:bg-gray-50"
                    >
                      <td className="left-col">
                        {item.Posts?.[0]?.Thumbnail && (
                          <img
                            src={
                              item.Posts[0].Thumbnail ||
                              "https://img.freepik.com/premium-vector/vector-flat-illustration-grayscale-avatar-user-profile-person-icon-profile-picture-business-profile-woman-suitable-social-media-profiles-icons-screensavers-as-templatex9_719432-1351.jpg?semt=ais_hybrid&w=740"
                            }
                            alt="Thumbnail"
                            className="w-10 h-10 rounded-xl"
                          />
                        )}
                      </td>
                      <td
                        className="py-4 px-4 text-[0.85rem] text-nowrap truncate max-w-[150px] lg:min-w-[260px]"
                        title={item?.Posts?.[0]?.Text || "N/A"}
                      >
                        {item?.Posts?.[0]?.Text || "N/A"}
                      </td>
                      <td className="p-4 min-w-[100px]">
                        <div className="flex items-center space-x-0.5 flex-wrap">
                          {item.Posts?.map((post: any) => {
                            let icon;
                            switch (post.SocialNetwork) {
                              case "Facebook":
                                icon = (
                                  <a
                                    href={post.URL}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                  >
                                    <FaFacebookF
                                      className="text-[1.50rem] bg-[#1877F2] p-1.5 text-[#fff] rounded-lg"
                                      style={{
                                        boxShadow: "0px 3px 10px 0px #1976D252",
                                      }}
                                    />
                                  </a>
                                );
                                break;
                              case "Instagram":
                                icon = (
                                  <a
                                    href={post.URL}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                  >
                                    <FaInstagram
                                      className="text-[1.50rem] p-1.5 text-white rounded-lg"
                                      style={{
                                        background:
                                          "linear-gradient(45deg, #feda75, #fa7e1e, #d62976, #962fbf, #4f5bd5)",
                                        boxShadow:
                                          "0px 4px 4px 0px rgba(0, 0, 0, 0.2)",
                                      }}
                                    />
                                  </a>
                                );
                                break;
                              case "LinkedIn":
                                icon = (
                                  <a
                                    href={post.URL}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                  >
                                    <img src={linkedinIcon} alt="LinkedIn" className="w-6 h-6" />
                                  </a>
                                );
                                break;
                              case "Twitter":
                                icon = (
                                  <a
                                    href={post.URL}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                  >
                                    <FaXTwitter
                                      className="text-[1.50rem] bg-[#000] p-1.5 text-[#fff] rounded-lg"
                                      style={{
                                        boxShadow:
                                          "0px 4px 8px rgba(0, 0, 0, 0.2)",
                                      }}
                                    />
                                  </a>
                                );
                                break;
                              case "Youtube":
                                icon = (
                                  <a
                                    href={post.URL}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                  >
                                    <RiYoutubeFill
                                      className="text-[1.50rem] bg-[#E21A20] p-1.5 text-[#fff] rounded-lg"
                                      style={{
                                        boxShadow: "0px 4px 4px 0px #E21A204F",
                                      }}
                                    />
                                  </a>
                                );
                                break;
                              default:
                                return null;
                            }
                            return icon;
                          })}
                        </div>
                      </td>
                      <td className="p-4 text-sm">
                        <span
                          className={`px-2 py-1.5 rounded-lg text-[0.85rem] font-normal ${item.Validation === "Validated"
                            ? "bg-lightblue text-primary"
                            : item.Validation === "Unvalidated"
                              ? "bg-red-100 text-red-600"
                              : "bg-blue-100 text-blue-600"
                            }`}
                        >
                          {item.Validation || "Pending"}
                        </span>
                      </td>
                      <td className="p-4 text-[0.85rem]">
                        <span>
                          {new Date(
                            item.Posts[0].TimeStamp
                          ).toLocaleDateString()}
                        </span>
                      </td>
                      <td className="p-4 text-[0.85rem]">{item.NumberOfAssociations}</td>
                      <td className="right-col">
                        <div className="flex items-center gap-1">
                          <button
                            className="px-2 py-2 rounded-lg bg-lightblue cursor-pointer text-sm text-primary"
                            onClick={() => openEntitiesModal(item.PublicationID)}
                          >
                            <FaEye />
                          </button>

                          <button
                            className="px-2 py-2 rounded-lg bg-[#21CE9E1A] cursor-pointer text-sm text-[#21CE9E]"
                            onClick={() => handleLinkNavigation(item.PublicationID)}
                          >
                            <FaLink />
                          </button>

                          <button
                            className="px-2 py-2 rounded-lg text-sm cursor-pointer text-[#FB4242] bg-[#FB42421A]"
                            onClick={() => {
                              setSelectedPublicationId(item.PublicationID);
                              setIsTagModalOpen(true);
                            }}
                          >
                            <MdAddCircleOutline />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={7} className="text-center py-4">
                      {t("publication.noPublicationsFound")}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
            <div className="mt-4 px-6">
              <Pagination
                currentPage={currentPage}
                totalItems={data.length}
                itemsPerPage={itemsPerPage}
                onPageChange={handlePageChange}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Mobile UI */}
      <div className="mt-6 rounded-xl hidden xs:max-sm:block">
        <div className="bg-white overflow-hidden">
          <div className="overflow-x-auto relative">
            <table className="min-w-full border-collapse text-left text-sm">
              <thead>
                <tr className="border-b border-gray-200 font-bold tracking-tight bg-[#EDF1F575] text-sm">
                  <th className="px-6 py-5">{t("publication.thumbnail")}</th>
                </tr>
              </thead>
              <tbody className="flex flex-col">
                {loading ? (
                  <tr>
                    <td colSpan={7}>
                      <Loader />
                    </td>
                  </tr>
                ) : paginatedData.length > 0 ? (
                  paginatedData.map((item: any) => (
                    <React.Fragment key={item.PublicationID}>
                      <tr className="border-b border-gray-100 hover:bg-gray-50">
                        <span className="py-4 px-8 flex justify-between w-full">
                          <td className="">
                            {item.Posts?.[0]?.Thumbnail && (
                              <img
                                src={
                                  item.Posts[0].Thumbnail
                                    ? item.Posts[0].Thumbnail
                                    : "https://img.freepik.com/premium-vector/vector-flat-illustration-grayscale-avatar-user-profile-person-icon-profile-picture-business-profile-woman-suitable-social-media-profiles-icons-screensavers-as-templatex9_719432-1351.jpg?semt=ais_hybrid&w=740"
                                }
                                alt="Thumbnail"
                                className="w-15 h-15 rounded-xl"
                              />
                            )}
                          </td>
                          <td className="flex flex-col items-end">
                            <td className="flex w-full gap-2 flex justify-end items-center">
                              <button
                                className="px-2 py-2 rounded-lg mt-3.5 text-md bg-[#21CE9E1A] cursor-pointer text-xl text-[#21CE9E]"
                                onClick={() => openEntitiesModal(item.PublicationID)}
                              >
                                <FaEye />
                              </button>
                              <button
                                className="px-2 py-2 rounded-lg mt-3.5 text-md cursor-pointer text-[#FB4242] bg-[#FB42421A]"
                                onClick={() => handleLinkNavigation(item.PublicationID)}
                              >
                                <FaLink />
                              </button>
                              <button
                                className="px-2 py-2 rounded-lg mt-3.5 text-md bg-lightblue cursor-pointer text-primary"
                                onClick={() => {
                                  setSelectedPublicationId(item.PublicationID);
                                  setIsTagModalOpen(true);
                                }}
                              >
                                <MdAddCircleOutline />
                              </button>
                            </td>
                            <td className="flex mt-3 items-end justify-end space-x-2">
                              {item.Posts?.map((post: any, i: number) => {
                                let icon;
                                switch (post.SocialNetwork) {
                                  case "Facebook":
                                    icon = (
                                      <span className="h-6 w-6">
                                        <FaFacebookF
                                          className="text-[1.78rem] bg-[#1877F2] p-1.5 text-[#fff] rounded-lg"
                                          style={{
                                            boxShadow:
                                              "0px 3px 10px 0px #1976D252",
                                          }}
                                        />
                                      </span>
                                    );
                                    break;
                                  case "Instagram":
                                    icon = (
                                      <span className="h-6 w-6">
                                        <FaInstagram
                                          className="text-[1.78rem] p-1.5 text-white rounded-lg"
                                          style={{
                                            background:
                                              "linear-gradient(45deg, #feda75, #fa7e1e, #d62976, #962fbf, #4f5bd5)",
                                            boxShadow:
                                              "0px 4px 4px 0px rgba(0, 0, 0, 0.2)",
                                          }}
                                        />
                                      </span>
                                    );
                                    break;
                                  case "LinkedIn":
                                    icon = (
                                      <a
                                        href={post.URL}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                      >
                                        <img src={linkedinIcon} alt="LinkedIn" className="w-6 h-6" />
                                      </a>
                                    );
                                    break;
                                  case "Twitter":
                                    icon = (
                                      <span className="h-6 w-6">
                                        <FaXTwitter
                                          className="text-[1.78rem] bg-[#000] p-1.5 text-[#fff] rounded-lg"
                                          style={{
                                            boxShadow:
                                              "0px 4px 8px rgba(0, 0, 0, 0.2)",
                                          }}
                                        />
                                      </span>
                                    );
                                    break;
                                  case "Youtube":
                                    icon = (
                                      <span className="h-6 w-6">
                                        <RiYoutubeFill
                                          className="text-[1.78rem] bg-[#E21A20] p-1.5 text-[#fff] rounded-lg"
                                          style={{
                                            boxShadow:
                                              "0px 4px 4px 0px #E21A204F",
                                          }}
                                        />
                                      </span>
                                    );
                                    break;
                                  default:
                                    return null;
                                }

                                return (
                                  <a
                                    key={i}
                                    href={post.URL}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                  >
                                    {icon}
                                  </a>
                                );
                              })}
                            </td>
                          </td>
                        </span>

                        <span className="flex items-center justify-between">
                          <td className="py-4 px-8 text-sm">
                            {new Date(
                              item.Posts[0].TimeStamp
                            ).toLocaleDateString()}
                          </td>
                          <td className="py-4 px-8 text-sm">
                            {item.NumberOfAssociations}
                          </td>
                        </span>
                        <td
                          className="py-4 px-8 max-w-[200px] text-sm text-nowrap truncate"
                          title={item?.Posts?.[0]?.Text || "N/A"}
                        >
                          {item?.Posts?.[0]?.Text || "N/A"}
                        </td>
                      </tr>
                    </React.Fragment>
                  ))
                ) : (
                  <tr>
                    <td colSpan={7} className="text-center py-4">
                      {t("publication.noPublicationsFound")}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
            {!loading && (
              <div className="mt-4 px-8">
                <Pagination
                  currentPage={currentPage}
                  totalItems={data.length}
                  itemsPerPage={itemsPerPage}
                  onPageChange={handlePageChange}
                />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Tag Modal */}
      <TagModal
        isOpen={isTagModalOpen}
        tagInput={tagInput}
        onChangeInput={setTagInput}
        onAddTag={handleAddTag}
        onClose={closeTagModal}
        publicationId={
          selectedPublicationId !== null
            ? String(selectedPublicationId)
            : undefined
        }
      />

      {/* Entities Related Modal */}
      <Modal
        isOpen={isEntitiesModalOpen}
        onClose={() => setIsEntitiesModalOpen(false)}
        label={t("publication.entitiesModalTitle")}
      >
        {entitiesLoading ? (
          <p className="text-center p-4">{t("publication.loading")}</p>
        ) : entitiesData.length > 0 ? (
          <div className="overflow-x-auto mt-2">
            <table className="min-w-full border-collapse text-left">
              <thead>
                <tr className="border-b border-gray-200 text-[#9C9C9C] font-normal text-sm">
                  <th className="p-3">{t("publication.entityKey")}</th>
                  <th className="p-3 text-nowrap">{t("publication.entityValue")}</th>
                </tr>
              </thead>
              <tbody>
                {entitiesData.map((item) => (
                  <tr
                    key={item.AttributeId}
                    className="border-b border-gray-100 hover:bg-gray-50"
                  >
                    <td className="p-3 font-medium text-nowrap">{item.Key}</td>
                    <td className="p-3 font-medium text-nowrap">
                      {item.Value}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p className="text-center p-4">{t("publication.noDataAvailable")}</p>
        )}
      </Modal>
    </div>
  );
}

export default Publications;