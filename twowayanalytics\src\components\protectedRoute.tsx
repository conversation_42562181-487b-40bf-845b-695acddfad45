// import React from "react";
// import { Navigate } from "react-router-dom";
// import { useAuth } from "../context/authContext";

// const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
//   const { isAuthenticated } = useAuth();

//   return isAuthenticated ? <>{children}</> : <Navigate to="/login" replace />;
// };

// export default ProtectedRoute;

import React from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useAuth } from "../context/authContext";

const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, user } = useAuth();
  const location = useLocation();

  // Determine the user's role from localStorage or auth context
  const userData = user || JSON.parse(localStorage.getItem("UserData") || "{}");
  const role = userData.role?.toLowerCase() || "unknown";
  const isInviteDisabled = role === "user";

  // Define allowed routes for the "user" role
  const allowedRoutesForUser = [
    "/analaytics",
    "/group-posts",
    "/publications",
    "/entities",
    "/instructions",
    "/contactUs",
    "/alerts/scope",
    "/alerts/scope/:id",
    "/alerts/sentiment",
    "/alerts/sentiment/:id",
    "/alerts/mentions",
    "/alerts/mentions/:id",
    "/alert/Metrics_table",
    "/alerts/sentiment_table",
    "/alerts/mention_table",
  ].map((route) => route.replace(/\/:id$/, "")); // Handle dynamic :id routes

  // Check if the current route is allowed for the user's role
  const isRouteAllowed = () => {
    if (!isInviteDisabled) {
      // Admin or other roles can access all protected routes
      return true;
    }
    // For "user" role, check if the route is in the allowed list
    return allowedRoutesForUser.some((allowedRoute) =>
      location.pathname.startsWith(allowedRoute)
    );
  };

  // Redirect logic
  if (!isAuthenticated) {
    // Redirect to login if not authenticated
    return <Navigate to="/login" replace state={{ from: location }} />;
  }

  if (!isRouteAllowed()) {
    // Redirect to /analaytics if the route is not allowed for the user's role
    return <Navigate to="/analaytics" replace state={{ from: location }} />;
  }

  // Render the children if authenticated and route is allowed
  return <>{children}</>;
};

export default ProtectedRoute;
