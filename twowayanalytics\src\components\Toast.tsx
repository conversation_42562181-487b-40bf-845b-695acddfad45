import { Toaster, toast } from "react-hot-toast";

export const showSuccessToast = (msg: any) => {
    toast.success(msg);
  };

export const showErrorToast = (msg:any) => {
    toast.error(msg);
}
export const showWarnToast = (message: any) => {
    toast(message, {
      icon: "⚠️",
      style: {
        background: "#facc15",
        color: "#000",
      },
    });
  };
const Toast =()=>{
    return (
        <Toaster position="top-center" reverseOrder={false}/>
    )
}  
export default Toast;