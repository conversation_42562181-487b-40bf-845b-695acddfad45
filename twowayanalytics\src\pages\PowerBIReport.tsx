import { useEffect, useState } from "react";
import { models } from "powerbi-client";
import { PowerBIEmbed } from "powerbi-client-react";
import api from "../services/apiService";

interface PowerBITokenResponse {
  embedToken: string;
  expiresAt: number;
  reportId: string;
  datasetId: string;
}

const PowerBIReport = () => {
  const [accessToken, setAccessToken] = useState<string>("");
  const [reportId, setReportId] = useState<string>("");
  const [embedUrl, setEmbedUrl] = useState<string>("");
  const [isMobile, setIsMobile] = useState<boolean>(false);
  const [hasToken, setHasToken] = useState<boolean | null>(null);
  const [isTwitterDisabled, setIsTwitterDisabled] = useState<boolean>(true);
  const [msAccessToken, setMsAccessToken] = useState<string>("");

  const FREE_REPORT_ID = "72d9236f-cce7-4f98-920f-ad371b482fc3";
  const TENANT_ID = "ccdd6c63-ac71-4ab9-ae96-54b0443f664e";

  useEffect(() => {
    const userData = JSON.parse(localStorage.getItem("UserData") || "{}");
    const pkg = userData.pkg?.toLowerCase() || "unknown";
    setIsTwitterDisabled(pkg === "unknown" || pkg === "free");
    setMsAccessToken(userData.ms_access_token || "");
  }, []);

  const fetchEmbedToken = async () => {
    try {
      const now = Date.now();
      const cacheKey = isTwitterDisabled ? "powerbi_token_free" : "powerbi_token";
      const cached = localStorage.getItem(cacheKey);

      if (cached) {
        const parsed = JSON.parse(cached) as PowerBITokenResponse;
        if (parsed.expiresAt > now + 60000 * 5) {
          console.log("Using cached token");
          setAccessToken(parsed.embedToken);
          setReportId(parsed.reportId);
          setEmbedUrl(`https://app.powerbi.com/reportEmbed?reportId=${parsed.reportId}&ctid=${TENANT_ID}&navContentPaneEnabled=false&chromeless=1`);
          setHasToken(true);
          return;
        }
      }

      console.log("Fetching new token");

      let data: PowerBITokenResponse;

      if (isTwitterDisabled) {
        data = await api.getPowerBIToken<PowerBITokenResponse>("power-bi/token-free-user");
        localStorage.setItem("powerbi_token_free", JSON.stringify(data));
        setReportId(data.reportId || FREE_REPORT_ID);
      } else {
        data = await api.getPowerBIToken<PowerBITokenResponse>("power-bi/token");
        localStorage.setItem("powerbi_token", JSON.stringify(data));
        setReportId(data.reportId);
      }

      setAccessToken(data.embedToken);
      // 'https://app.powerbi.com/reportEmbed?reportId=72d9236f-cce7-4f98-920f-ad371b482fc3&ctid=ccdd6c63-ac71-4ab9-ae96-54b0443f664e&navContentPaneEnabled=false&chromeless=1'
      setEmbedUrl(`https://app.powerbi.com/reportEmbed?reportId=${data.reportId || FREE_REPORT_ID}&ctid=${TENANT_ID}&navContentPaneEnabled=false&chromeless=1`);
      setHasToken(true);
    } catch (error) {
      console.error("Failed to fetch token:", error);
      setHasToken(false);
      localStorage.removeItem(isTwitterDisabled ? "powerbi_token_free" : "powerbi_token");
      if (isTwitterDisabled) {
        // Fallback to FREE_REPORT_ID for free users in case of error
        setReportId(FREE_REPORT_ID);
        setEmbedUrl(`https://app.powerbi.com/reportEmbed?reportId=${FREE_REPORT_ID}&ctid=${TENANT_ID}&navContentPaneEnabled=false&chromeless=1`);
      }
    }
  };

  useEffect(() => {
    if (isTwitterDisabled && !msAccessToken) {
      setHasToken(false);
      return;
    }

    fetchEmbedToken();

    const handleResize = () => setIsMobile(window.innerWidth < 768);
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [isTwitterDisabled, msAccessToken]);

  if (hasToken === null) {
    return (
      <div className="flex justify-center items-center min-h-[96vh] shadow bg-white rounded-3xl">
        <p className="text-lg text-gray-600">Loading report...</p>
      </div>
    );
  }

  return (
    <div className="flex justify-center items-center min-h-[96vh] shadow bg-white rounded-3xl w-full">
      <div className="w-full h-[90vh] rounded-3xl overflow-hidden">
        {!accessToken || !reportId || !embedUrl || hasToken === false ? (
          <p className="text-center text-gray-500 p-4">Unable to load report. Please try again later.</p>
        ) : (
          // <PowerBIEmbed
          //   embedConfig={{
          //     type: "report",
          //     id: reportId,
          //     embedUrl: embedUrl,
          //     accessToken: accessToken,
          //     tokenType: models.TokenType.Embed,
          //     permissions: models.Permissions.All,
          //     settings: {
          //       ...(isMobile && {
          //         layoutType: models.LayoutType.MobilePortrait,
          //       }),
          //       panes: {
          //         filters: { visible: false },
          //         pageNavigation: { visible: true },
          //       },
          //     },
          //   }}
          //   eventHandlers={
          //     new Map([
          //       ["loaded", () => console.log("Report loaded")],
          //       ["rendered", () => console.log("Report rendered")],
          //       ["error", (event: any) =>
          //         console.error("Power BI Error", event.detail)],
          //     ])
          //   }
          //   cssClassName="w-full h-full report-style-class"
          //   getEmbeddedComponent={(embeddedReport) => {
          //     console.log("Embedded Report instance:", embeddedReport);
          //   }}
          // />
          <PowerBIEmbed
            embedConfig={{
              type: "report",
              id: reportId,
              embedUrl: embedUrl,
              accessToken: accessToken,
              tokenType: models.TokenType.Embed,
              permissions: models.Permissions.All,
              settings: {
               // background: models.BackgroundType.Transparent,
                filterPaneEnabled: false,
                bars: {
                  actionBar: {
                    visible: false,
                  },
                },
                panes: {
                  filters: {
                    expanded: false,
                    visible: false,
                  },
                  pageNavigation: {
                    visible: true,
                    position: models.PageNavigationPosition.Left,
                  },
                  bookmarks: {
                    visible: false,
                  },
                  fields: {
                    expanded: false,
                    visible: false,
                  },
                  selection: {
                    visible: false,
                  },
                  syncSlicers: {
                    visible: false,
                  },
                  visualizations: {
                    expanded: false,
                    visible: false,
                  },
                },
                ...(isMobile && {
                  layoutType: models.LayoutType.MobilePortrait, 
                }),
              },
            }}
            eventHandlers={new Map([
              ["loaded", () => console.log("Report loaded")],
              ["rendered", () => console.log("Report rendered")],
              ["error", (event: any) => console.error("Power BI Error", event.detail)],
            ])}
            cssClassName="w-full h-full report-style-class"
            getEmbeddedComponent={(embeddedReport) => {
              console.log("Embedded Report instance:", embeddedReport);
            }}
          />


        )}
      </div>
    </div>
  );
};

export default PowerBIReport;